<?php

$todaymorning = strtotime(current_time("Y-m-d"));//当天0点
$todaynight = $todaymorning + 24 * 3600 - 1;//当天23.59
$lastdaynight = $todaymorning - 1;//昨天晚间23.59
$lastdaymorning = $lastdaynight - 24 * 3600 + 1;//昨天0点
$tomorrowmorning = $todaymorning + 24 * 3600;//明天0点
global $wpdb;

function show_data(){


 /*   $str2='SELECT meta_value FROM  `'.$wpdb->prefix.'usermeta` WHERE meta_key =  "review_now_time"';
    $res2=$wpdb->get_results($str2);
    $active_num3=0;
    $active_num5=0;
    foreach($res2 as $re){
        $threedaytime=current_time("timestamp")-24*3*3600;
        if($re->meta_value>$threedaytime){
            $active_num3+=1;
        }
    }
    foreach($res2 as $re){
        $fivedaytime=current_time("timestamp")-24*5*3600;
        if($re->meta_value>$fivedaytime){
            $active_num5+=1;
        }
    }
    $rate3=number_format($active_num3/$res2*100, 2, '.', '');
    $rate5=number_format($active_num5/$res2*100, 2, '.', '');*/


   /* $str='SELECT meta_value FROM  `'.$wpdb->prefix.'usermeta` WHERE meta_key =  "user_sign_time"';
    $res3=$wpdb->get_results($str);
    $sign_today_active=0;//今日活跃分享用户

    foreach($res3 as $re){
        if($re->meta_value>$todaymorning){
            $sign_today_active+=1;
        }
    }

    $str='SELECT meta_value FROM  `'.$wpdb->prefix.'usermeta` WHERE meta_key =  "user_share_time"';
    $res4=$wpdb->get_results($str);
    $share_today_active=0;//今日活跃分享用户

    foreach($res4 as $re){
        if($re->meta_value>$todaymorning){
            $share_today_active+=1;
        }
    }
    echo '<p>3天内活跃复习计划活跃人数：'.$active_num3.'人</p>';
    echo '<p>3天内活跃与总人数比：'.$rate3."%</p>";
    echo '<p>5天内活跃复习计划活跃人数：'.$active_num5.'人</p>';
    echo '<p>5天内活跃与总人数比：'.$rate5."%</p>";
    echo '<p>开始与当前平均时间差：'.show_select().'</p>';
    echo '<p>今日签到人数：'.$sign_today_active.'</p>';
    echo '<p>今日分享人数：'.$share_today_active.'</p>';


    echo '</div>';*/

}




?>

<style type="text/css">
    .amts .form-table th, .amts .form-table td {
        border: none !important;
    }

    .modelClass {
        margin: 30px auto;
    }

    #selectModel {
        display: none;
    }

    #select {
        display: none;
    }

    #modelname {
        color: red;
    }

    #warn {
        color: red;
        margin-bottom: 20px;
    }

    #refreshBtn {
        display: none;
    }

    #addResetBtn {
        margin-top: 20px;

    }

    .list-table {
        width: 100%;
        background: white;
        height: 2000px;
        display: none;

    }

    .height40 {
        height: 40px;
        line-height: 40px;
    }

    .heightAuto {
        height: auto;

    }

    .list-title {
        width: 100%;
        text-align: center;
        border-bottom: 1px solid darkgray;
        float: left;

    }

    .list-line {
        width: 100%;
        text-align: center;
        border-bottom: 1px solid darkgray;
        float: left;
    }

    .list-title div, .list-line div {
        width: 10%;
        float: left;
        display: block;
        min-height: 30px;
        text-align: center;
    }

    .list-title div:nth-child(3), .list-line div:nth-child(3) {
        width: 60%;

    }
</style>

<script src="<?php bloginfo('template_url'); ?>/js/jquery-1.11.3.min.js" type="text/javascript"></script>

<div class="wrap amts">
    <h2>用户复习计划分析系统</h2>
    <?php
    $str='SELECT COUNT( * ) FROM  `'.$wpdb->prefix.'usermeta` WHERE meta_key =  "review_mes"';
    $res=$wpdb->get_var($str);
    echo '<div style="width: 100%;margin-top: 10px">';
    echo '<h4>数据</h4>';
    echo '<p>目前拥有复习计划用户：'.$res.'人</p>';


    $str2='SELECT meta_value FROM  `'.$wpdb->prefix.'usermeta` WHERE meta_key =  "review_now_time"';
    $res2=$wpdb->get_results($str2);
    //myDump($res2);
    $active_num3=0;
    $active_num5=0;
    $threedaytime=current_time("timestamp")-24*3*3600;
    $fivedaytime=current_time("timestamp")-24*5*3600;
    foreach($res2 as $r){

        if($r->meta_value>$threedaytime){
            $active_num3+=1;
        }

    }


    foreach($res2 as $r){
        if($r->meta_value>$fivedaytime){
            $active_num5+=1;
        }
    }

    $rate3=number_format($active_num3/$res*100, 2, '.', '');
    $rate5=number_format($active_num5/$res*100, 2, '.', '');


    echo '<p>3天内活跃复习计划活跃人数：'.$active_num3.'人</p>';
    echo '<p>3天内活跃与总人数比：'.$rate3."%</p>";
    echo '<p>5天内活跃复习计划活跃人数：'.$active_num5.'人</p>';
    echo '<p>5天内活跃与总人数比：'.$rate5."%</p>";


     $str3='SELECT meta_value FROM  `'.$wpdb->prefix.'usermeta` WHERE meta_key =  "user_sign_time"';
     $res3=$wpdb->get_results($str3);
     $sign_today_active=0;//今日活跃分享用户

    foreach($res3 as $re){
         if($re->meta_value>$todaymorning){
             $sign_today_active+=1;
         }
     }

     $str4='SELECT meta_value FROM  `'.$wpdb->prefix.'usermeta` WHERE meta_key =  "user_share_time"';
     $res4=$wpdb->get_results($str4);
     $share_today_active=0;//今日活跃分享用户

     foreach($res4 as $re){
         if($re->meta_value>$todaymorning){
             $share_today_active+=1;
         }
     }
     echo '<p>今日签到人数：'.$sign_today_active.'</p>';
     echo '<p>今日分享人数：'.$share_today_active.'</p>';




    $active_today_review_users=array();

    $str='SELECT user_id,meta_value FROM  `'.$wpdb->prefix.'usermeta` WHERE meta_key =  "review_now_time"';
    $res=$wpdb->get_results($str);
    foreach($res as $re){
        if($re->meta_value>$todaymorning){
            array_push($active_today_review_users,$re->user_id);
        }
    }

    //myDump($active_today_review_users);
    $active_today_review_users_num=count($active_today_review_users);
    $total_over_num=0;
    foreach($active_today_review_users as $u){
        $o=get_user_meta($u,"today_review_over_num",true);
        $total_over_num+=$o;
    }
    $over_num_ave=round($total_over_num/$active_today_review_users_num);


    echo '<p>今日活跃用户复习计划拖延数：'.$over_num_ave.'</p>';
    echo "<p>今日复习计划活跃总人数：$active_today_review_users_num</p>";


    echo '</div>';
    ?>


    <?php //show_data(); ?>

    <div class="modelClass">
        <h4>当前模式：<span id="modelname">纵览模式</span>&nbsp;&nbsp;&nbsp;</h4>
        <button id="changeModelBtn">更换</button>
    </div>
    <div id="watchModel"></div>
    <div id="selectModel">
        <p>输入用户ID：</p>
        <input id="searchBtn" type="text"/>
        <button id="submitBtn">搜索</button>
        <p id="warn"></p>
    </div>
    <div class="list-table">
    </div>
    <div class="list-table-i">
    </div>
</div>

<script>
    var model = 1;//模式
    var modelname = $('#modelname');
    var changeModelBtn = $('#changeModelBtn');


    var warn = $("#warn");
    var search_user = "";
    var submitBtn = $("#submitBtn");
    var searchBtn = $("#searchBtn");


    var watchModel = $('#watchModel');
    var selectModel = $('#selectModel');

    var list_table = $(".list-table");

    changeModelBtn.bind('click', function () {
        if (model == 1) {
            model = 2;
            modelname.html("查询模式");
            selectModel.css("display", "block");
            watchModel.css("display", "none");
            warn.html("");
            list_table.html("");
            list_table.css("display","block");


        } else {
            model = 1;
            modelname.html("纵览模式");
            selectModel.css("display", "none");
            watchModel.css("display", "block");
            warn.html("");
            list_table.html("");
            list_table.css("display","none");

        }
    });


    function addElement(res,jqObj) {
        var review_mes_arr=(res.review_mes)?JSON.parse(res.review_mes):[];//解析review_mes

        var tr = $('<div user_id="' + res.user_id + '" class="list-line heightAuto"></div>');
        if (!res.user_nickname) {
            res.user_nickname = "暂无昵称"
        }
        var tdnickname = $('<div><p>' + res.user_nickname + '</p></div>');
        var tdid = $('<div><p>' + res.user_id + '</p></div>');
        var tdreview = $('<div></div>');
        var tdlogin = "";//开始与最后次复习时间差
        var tddelete = "";//全部删除

        if (review_mes_arr.length==0) {
            tddelete = $('<div><p>空复习计划</p></div>');
            tdlogin = $('<div><p>数据不全</p></div>');
            tdreview=$('<div><p>空复习计划</p></div>');
        } else {

            var review_mes = JSON.parse(res.review_mes);
            for (var i = 0; i < review_mes.length; i++) {
                var outspan = $('<span style="float:left;padding:8px;border:1px solid darkgray;margin:3px;" book=' + review_mes[i].post_id + ' user_id=' + res.user_id + '></span>');
                var cat = (review_mes[i].model == "myBook") ? "SH" : review_mes[i].model;
                var span0 = $('<span style="margin:5px;">CT:<em style="color:red">' + cat + '</em></span>');
                var span1 = $('<span style="margin:5px">ID:' + review_mes[i].post_id + '</span>');
                var span2 = $('<span style="margin:5px">TS:' + review_mes[i].finishTimes + '</span>');
                var span3 = $('<a href="javascript:;" style="margin 10px;color:blue;text-decoration: none;" book=' + review_mes[i].post_id + ' user_id=' + res.user_id + '>X</a>');
                span3.bind("click", function () {
                    var user_id = $(this).attr("user_id");
                    var book_id = $(this).attr("book");
                    var _that=this;

                    if (confirm("确定删除？")) {
                        $.ajax({
                            url: "<?php echo admin_url("admin-ajax.php");?>",
                            type: 'post',
                            data: {
                                action: 'deleteSingleReviewMes',
                                user_id: user_id,
                                book_id: book_id
                            },
                            success: function (data, status) {

                                //将结果转为字符串，去掉收尾空格
                               var res = data.toString().trim();
                                console.log(res);
                                if (res==10) {
                                    alert("删除成功");
                                    $(_that.parentNode).remove();

                                }else if(res==20){
                                    alert("没有找到这个单词本");
                                    window.location=window.location;

                                } else {
                                    alert("删除失败");
                                }
                            }
                        });//ajax删除

                    }
                });

                outspan.append(span0, span1, span2, span3);

                tdreview.append(outspan);
            }

            tdlogin = $('<div><p>'+res.dis_time+'</p></div>');

            tddelete = $('<div><p><a href="javascript:;">全部删除</a></p></div>');
            tddelete.bind("click", function () {
                if (confirm("确定删除？")) {
                    var user_id = $(this.parentNode).attr("user_id");//获取userid
                    $.ajax({
                        url: "<?php echo admin_url("admin-ajax.php");?>",
                        type: 'post',
                        data: {
                            action: 'deleteAllReviewMes',
                            user_id: user_id
                        },
                        success: function (data, status) {
                            //将结果转为字符串，去掉收尾空格
                            var res = data.toString().trim();
                            if (res != 0) {
                                alert("删除成功");
                                window.location=window.location;

                            } else {
                                alert("删除失败");
                            }
                        }
                    });//ajax删除
                }
            })
        }
        tr.append(tdnickname, tdid, tdreview, tdlogin, tddelete);
        jqObj.append(tr);


    }

    function addTitle() {
        var div = "<div class='list-title height40'><div>用户昵称</div><div>用户ID</div><div>复习计划</div><div>开始与当前时间差</div><div>清除</div></div>";
        list_table.append(div);
    }


    submitBtn.bind("click", function () {
        if (!searchBtn.val()) {
            warn.html("无输入数据");
            search_user = "";
        } else {
            search_user = searchBtn.val();//搜索单词是输入框中的值
            searchBtn.val("");//重置
            list_table.html("");
            addTitle();
            warn.html("当前查询用户ID：" + search_user);

            $.ajax({
                url: "<?php echo admin_url("admin-ajax.php");?>",
                type: 'post',
                data: {
                    action: 'selectReviewMes',
                    user_id: search_user
                },
                success: function (data, status) {
                    //将结果转为字符串，去掉收尾空格
                    var res = data.toString().trim();

                    if (res != 0) {
                        //当结果不等于0
                        res = res.substring(0, res.length - 1);//去掉最后个字符0
                        console.log(res);
                       res = JSON.parse(res);//转为json对象
                       console.log(res);
                        addElement(res,list_table);
                        //alterTextArea(res);

                    } else {
                        warn.html("未找到");
                        search_user = "";
                    }
                }
            });

        }

    });//搜索按键


</script>
# 网站运维关键信息梳理

---

## 远程连接信息
- **服务器远程连接：**
  - 用户名：`root`
  - 密码：`MS_Showlin123`

---

## 数据库信息
- **数据库账号及密码：**
  - root：`LaKVyGqxrqu5PcdZ`
  - weiyouxiin：`TevSFsUyFPTjQwVA`
  - hlyyin：`OVpBEs39YpSddaw5`
  - cet4.hlyy.in：`wKprlaNTahuExSBv`
  - joco15.com：`6tMCjYhAIWw806gZ`

- **MySQL 配置文件路径：** `/usr/local/mysql-generic-5.5.40/my.cnf`
- **MySQL 数据存储目录：** `/home/<USER>/mysql-generic-5.5.40/`
- **MySQL Socket 文件路径：** `/tmp/mysql-generic-5.5.40.sock`

---

## FTP 信息
- **连接地址：** `101.200.73.234`
  - FTP 账号：`joco15.com`
  - 密码：`01MOSHENSHOW`
- **连接地址：** `101.200.73.234`
  - FTP 账号：`jocosurface`
  - 密码：` u0Zxwa5TVgTODH77 `

---

## 系统信息
- **操作系统：** CentOS 6.5 (Final)
- **内核版本：** 2.6.32-754.35.1.el6.x86_64
- **CPU 信息：**
  - 架构：x86_64
  - CPU 数量：4 核（2 线程每核，共 1 个插槽）
  - 主频：2.5 GHz
- **内存：**
  - 总内存：3832 MB
  - 已用内存：787 MB
  - 空闲内存：3044 MB
- **磁盘：**
  - 总大小：59 GB
  - 已用：47 GB
  - 可用：9.8 GB

---

## 环境配置信息
- **Web 服务信息：**
  - Nginx 配置文件路径：`/usr/local/nginx-generic-1.6/conf/nginx.conf`
  - PHP 配置文件路径：`/etc/php.ini`
- **面板信息：**
  - AMH 面板版本：6.0
  - 模块列表：
    - mysql-generic-5.5.40
    - nginx-generic-1.6
    - php-generic-5.3
    - phpmyadmin-4.9
    - lnmp-3.2

---

## WordPress 信息
- **安装路径：** `/home/<USER>/my_lnmp/domain/weiyouxi.in/web/`
- **数据库配置信息：**
  - 数据库名称：`weiyouxiin`
  - 用户名：`hlyyin`
  - 密码：`OVpBEs39YpSddaw5`

---

## 数据库状态
- **数据库大小分布：**
  - `weiyouxiin`：2020.49 MB
  - `mysql`：0.65 MB
  - `amh`：0.59 MB

- **表大小与行数分布（weiyouxiin）：**
  - 最大表：`tl_usermeta`，大小：1915.12 MB，行数：10,843,685
  - 其他重要表：
    - `tl_users`：66.54 MB，316,811 行
    - `tl_postmeta`：18.80 MB，18,305 行
    - `tl_posts`：7.20 MB，5,874 行
    - `tl_dict`：5.89 MB，9,304 行

---

## 性能参数
- **查询缓存：**
  - 缓存大小：`0`（未启用）
  - 缓存类型：`ON`
- **连接相关：**
  - 最大连接数：`151`
  - `wait_timeout`：28800 秒
  - `interactive_timeout`：28800 秒
  - `connect_timeout`：10 秒
- **其他参数：**
  - `tmp_table_size`：16 MB
  - `max_heap_table_size`：16 MB
  - `key_buffer_size`：16 MB
  - `table_open_cache`：64

---

## 其他信息
- **慢查询日志状态：**
  - 启用状态：`OFF`
  - 日志路径：`/home/<USER>/mysql-generic-5.5.40/iZ252ng1968Z-slow.log`
  - 阈值：`long_query_time` 为 10 秒

- **磁盘使用：**
  - MySQL 数据目录：总占用空间 2.1 GB

---

## 已启用插件
- **插件名称及描述：**
  - `addVip` - 后台增减用户 VIP 时间
  - `Akismet` - 垃圾评论保护工具
  - `clockinselect` - 打卡查询
  - `Custom Category Templates` - 自定义分类模板
  - `Disable Google Fonts` - 禁用 WordPress 默认 Google 字体加载
  - `Limit Login Attempts Reloaded` - 限制登录尝试次数
  - `My Custom Cron Plugin` - 自定义定时任务插件
  - `PHP Compatibility Checker` - 检查 PHP 版本兼容性
  - `reviewtest` - 复习计划测试工具
  - `WPFront User Role Editor` - 用户角色管理插件
  - `youzan_order` - 有赞订单分析

- **当前主题：**
  - 主题名称：`html5game`
  - 作者：`monster`
  - 描述：HTML5 游戏站主题

---

## 防火墙规则
- **开放的端口：**
  - 80（HTTP），443（HTTPS）
  - 21（FTP），8888，9999
  - 自定义端口范围：10100-10180
- **阻止的 IP：**
  - *************
  - **********
  - **************（所有协议已被阻止）

---

## 权限信息
- **当前用户：** `root`
- **用户权限：** `uid=0(root) gid=0(root)`
- **访问敏感目录：** 完整访问 `/root`
- **sudo 权限：** 拥有完整 sudo 权限，可执行所有命令
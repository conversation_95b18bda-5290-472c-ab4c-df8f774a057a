<?php
/*
Template Name:todayReview
*/

if ($_GET['action']) {
    session_start();
    session_unset();
}

pcVisitHandle();




$userData = new userDataTest();//初始化用户对象


$reviewMesHandle = new reviewMesHandle($userData->user_id, $userData->isHeikaVip, current_time("timestamp"));//初始化记忆系统对象
get_header();


if ($userData->isLogin) {



    global $post;
    if (empty($reviewMesHandle->todayReviewArr)) {

        ?>
        <div class="weui-msg" style="margin-top: 170px">
            <div class="weui-msg__text-area">
                <p class="weui-msg__desc" style="font-size: 50px">
                    <span class="glyphicon glyphicon-edit" aria-hidden="true"></span>
                </p>

                <p class="weui-msg__desc">今天没有要复习的课程</p>

                <p class="weui-btn-area">
                    <a href="<?php echo home_url(); ?>" class="weui-btn weui-btn_primary" style="width: 60%;color:white">去学点新课</a>
                </p>
            </div>
        </div>


        <?php

    } else { ?>


        <?php


        if($userData->isHeikaVip!=1){

            $vipaction_text = get_option("g_vipaction_text");//VIP活动标语
            $vipaction_url = get_option("g_vipaction_single_pro_url");//主推商品页

            ?>

            <style>
                .top_adv{
                    width: 100%;
                    height: 40px;
                    position: fixed;
                    top: 0;
                    left: 0;
                    background-color: lightgoldenrodyellow;
                    font-size: 14px;
                    color: #d54e21;
                }

                .top_adv div{
                    float: left;
                    height: 40px;
                    line-height: 40px;
                }

                .top_adv div:first-child{
                    width: 8%;
                    text-align: center;
                }

                .top_adv div:nth-child(3){
                    width: 8%;
                    text-align: center;
                    float: right;
                }
            </style>

            <a href="<?php echo $vipaction_url;?>">

                <div class="top_adv">
                    <div>
                        <span class="glyphicon glyphicon-bullhorn" aria-hidden="true"></span>
                    </div>
                    <div><?php echo $vipaction_text;?></div>
                    <div>
                        <span class="glyphicon glyphicon-menu-right" aria-hidden="true"></span>
                    </div>
                </div>

            </a>

            <div style="width: 100%;height: 70px;float: left"></div>




        <?php }?>









        <?php
        echo '<div class="myBookBg">';

        foreach ($reviewMesHandle->todayReviewArr as $todayReview) {

            echo '<div id="' . $todayReview->post_id . '" class="myBookArea">';
            echo '<div class="myBookArea_left">';
            echo "<a href='" . home_url() . "/?p=" . $todayReview->post_id . "&backToReview=1" . "'>";
            echo '<div class="myBookArea_left_default" style="width: 20%;">';
            echo '<span style="font-size: 10px;border: 1px solid;padding: 1px;color:red">需复习</span>';
            echo '</div>';
            echo '<div class="myBookArea_left_title">';
            echo '<span>' . get_post($todayReview->post_id)->post_title . '</span>';
            echo '</div>';
            echo '<div class="myBookArea_left_button">';
            $catName = "";
            if ($todayReview->model == "myBook") {
                $catName = "生词本";
            } else {
                $catName = get_post_category_id($todayReview->post_id);
            }
            echo "<span style='color:darkgray;margin-left: 6%;width: 100%;text-align: left'>来自《" . $catName . "》</span>";
            echo '</div>';
            echo '</a>';
            echo '</div>';
            echo "<a href='" . home_url() . "/?p=" . $todayReview->post_id . "&backToReview=1" . "'>";
            echo '<div class="myBookArea_right">';
            echo '<span class="glyphicon glyphicon-menu-right" aria-hidden="true"></span>';
            echo '</div>';
            echo '</a>';
            echo '</div>';

        }
        echo '<div class="bottom_dis">';
        echo '</div>';
        echo '</div>';
    }

    ?>

    <script>
        $("body").css("background", "#F5F5F5");

        /*后退强制刷新*/

        var TYPE_NAVIGATE = 0, // 当前页面是通过点击链接，书签和表单提交，或者脚本操作，或者在url中直接输入地址，type值为0
            TYPE_RELOAD = 1, // 点击刷新页面按钮或者通过Location.reload()方法显示的页面，type值为1
            TYPE_BACK_FORWARD = 2, // 页面通过历史记录和前进后退访问时。type值为2
            TYPE_RESERVED = 255; // 任何其他方式，type值为255
        window.addEventListener('pageshow', function (e) {
            if (e.persisted || (window.performance && window.performance.navigation.type == TYPE_BACK_FORWARD)) {
                location.reload()
            }
        }, false)

    </script>



    <?php


} else {
    get_login_page();//提示登录

}
get_sidebar();
get_footer();
?>

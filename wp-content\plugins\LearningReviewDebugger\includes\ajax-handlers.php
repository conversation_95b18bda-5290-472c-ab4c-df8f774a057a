<?php
/**
 * Learning Review Debugger AJAX处理函数
 * 
 * 这个文件包含所有与学习复习调试相关的AJAX处理函数，负责处理来自调试器页面的请求。
 * 主要功能包括用户数据获取、复习计划管理、学习进度调试等。
 * 
 * 迁移说明：
 * 此文件从 wp-content/themes/shuimitao.online/plugins/functions/LearningReviewDebuggerAjax.php 迁移而来
 * 迁移到插件内部，确保功能独立于主题，提高代码的可维护性
 * 
 * @package LearningReviewDebugger
 * @version 2.0.0
 * <AUTHOR>
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 记录插件AJAX处理器加载
error_log('[LearningReviewDebugger] AJAX处理器开始加载');

/**
 * 获取用户基本数据的AJAX处理函数
 * 
 * 返回用户的各种元数据，包括复习时间、黑卡学习任务内容、打卡详情等
 */
function lrd_admin_review_get_user_data() {
    error_log('[LearningReviewDebugger] lrd_admin_review_get_user_data AJAX请求开始处理');
    
    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }
    
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 查询用户ID: ' . $user_id);
    
    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    error_log('[LearningReviewDebugger] 用户信息获取成功: ' . $user->display_name);
    
    // 获取用户订单信息
    $user_orders = get_user_meta($user_id, 'user_orders', true);
    $user_orders = $user_orders ? $user_orders : "";
    
    // 获取返现次数
    $cash_back_times = get_user_meta($user_id, 'cash_back_times', true);
    $cash_back_times = $cash_back_times ? $cash_back_times : "";
    
    // 获取学习天数返现机会
    $total_learning_days_cash_back_chance = get_user_meta($user_id, 'total_learning_days_cash_back_chance', true);
    $total_learning_days_cash_back_chance = $total_learning_days_cash_back_chance ? $total_learning_days_cash_back_chance : 0;
    
    // 获取复习时间和黑卡数据更新时间
    $alter_review_time = get_user_meta($user_id, 'alter_review_time', true);
    $alter_review_time = $alter_review_time ? $alter_review_time : 0;
    
    $alter_heika_data_time = get_user_meta($user_id, 'alter_heika_data_time', true);
    $alter_heika_data_time = $alter_heika_data_time ? $alter_heika_data_time : 0;
    
    // 获取黑卡学习任务内容
    $heika_daily_learning_task_content = get_user_meta($user_id, 'heika_daily_learning_task_content', true);
    $heika_daily_learning_task_content = $heika_daily_learning_task_content ? $heika_daily_learning_task_content : "";
    
    // 获取黑卡学习任务总数据
    $heika_daily_learning_task_content_total = get_user_meta($user_id, 'heika_daily_learning_task_content_total', true);
    $heika_daily_learning_task_content_total = $heika_daily_learning_task_content_total ? $heika_daily_learning_task_content_total : "";
    
    // 获取黑卡打卡详情
    $heika_check_in_detail = get_user_meta($user_id, 'heika_check_in_detail', true);
    $heika_check_in_detail = $heika_check_in_detail ? $heika_check_in_detail : "";
    
    // 获取学习详情 (修正字段名)
    $learn_detail = get_user_meta($user_id, 'learn_detail', true);
    $learn_detail = $learn_detail ? $learn_detail : "";

    // 获取今日学习详情 (修正字段名)
    $today_learn_detail = get_user_meta($user_id, 'today_learn_detail', true);
    $today_learn_detail = $today_learn_detail ? $today_learn_detail : "";
    
    // 获取复习计划
    $review_mes = get_user_meta($user_id, 'review_mes', true);
    $review_mes = $review_mes ? $review_mes : "";
    
    // 获取课程进度
    $course_rate_of_progress = get_user_meta($user_id, 'course_rate_of_progress', true);
    $course_rate_of_progress = $course_rate_of_progress ? $course_rate_of_progress : "";
    
    // 获取分类访问时间
    $cat_view_time = get_user_meta($user_id, 'cat_view_time', true);
    $cat_view_time = $cat_view_time ? $cat_view_time : "";
    
    // 计算连续打卡天数和总学习天数
    $continuously_learning_days = 0;
    $total_learning_days = 0;

    if (!empty($heika_check_in_detail)) {
        try {
            $heika_check_in_detail_arr = json_decode($heika_check_in_detail, true);
            if (is_array($heika_check_in_detail_arr) && count($heika_check_in_detail_arr) > 0) {
                // 计算连续打卡天数
                $start_key = count($heika_check_in_detail_arr) - 2; // 从倒数第二个开始
                for ($i = $start_key; $i >= 0; $i--) {
                    if (isset($heika_check_in_detail_arr[$i]['is_finish_heika_daily_learning_task']) &&
                        $heika_check_in_detail_arr[$i]['is_finish_heika_daily_learning_task'] == 1) {
                        $continuously_learning_days++;
                    } else {
                        break;
                    }
                }

                // 检查最后一天是否也完成了任务
                $last_sign_obj = end($heika_check_in_detail_arr);
                if (isset($last_sign_obj['is_finish_heika_daily_learning_task']) &&
                    $last_sign_obj['is_finish_heika_daily_learning_task'] == 1) {
                    $continuously_learning_days += 1;
                }

                // 计算总学习天数（最近110天内）
                $start_key = count($heika_check_in_detail_arr) - 1;
                $end_key = count($heika_check_in_detail_arr) > 110 ? count($heika_check_in_detail_arr) - 110 : 0;

                for ($i = $start_key; $i >= $end_key; $i--) {
                    if (isset($heika_check_in_detail_arr[$i]['is_finish_heika_daily_learning_task']) &&
                        $heika_check_in_detail_arr[$i]['is_finish_heika_daily_learning_task'] == 1) {
                        $total_learning_days++;
                    }
                }
            }
        } catch (Exception $e) {
            error_log('[LearningReviewDebugger] 计算打卡天数时出错: ' . $e->getMessage());
        }
    }

    // 构建返回数据
    $response = array(
        'user_id' => $user_id,
        'user_nickname' => $user->display_name,
        'user_orders' => $user_orders,
        'cash_back_times' => $cash_back_times,
        'total_learning_days_cash_back_chance' => $total_learning_days_cash_back_chance,
        'alter_review_time' => $alter_review_time,
        'alter_heika_data_time' => $alter_heika_data_time,
        'heika_daily_learning_task_content' => $heika_daily_learning_task_content,
        'heika_daily_learning_task_content_total' => $heika_daily_learning_task_content_total,
        'heika_check_in_detail' => $heika_check_in_detail,
        'learn_detail' => $learn_detail,
        'today_learn_detail' => $today_learn_detail,
        'continuously_learning_days' => $continuously_learning_days,
        'total_learning_days' => $total_learning_days,
        'review_mes' => $review_mes,
        'course_rate_of_progress' => $course_rate_of_progress,
        'cat_view_time' => $cat_view_time
    );
    
    error_log('[LearningReviewDebugger] 用户数据查询完成，返回字段数: ' . count($response));
    
    // 返回JSON格式的数据
    echo json_encode($response);
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_get_user_data', 'lrd_admin_review_get_user_data');
error_log('[LearningReviewDebugger] admin_review_get_user_data AJAX action已注册');

/**
 * 记录分类访问的AJAX处理函数
 */
function lrd_admin_record_category_visit() {
    error_log('[LearningReviewDebugger] lrd_admin_record_category_visit AJAX请求开始处理');
    
    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }
    
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    $cat_id = isset($_POST['cat_id']) ? intval($_POST['cat_id']) : 0;
    
    error_log('[LearningReviewDebugger] 记录分类访问 - 用户ID: ' . $user_id . ', 分类ID: ' . $cat_id);
    
    // 验证参数
    if ($user_id <= 0 || $cat_id <= 0) {
        error_log('[LearningReviewDebugger] 参数验证失败 - 用户ID: ' . $user_id . ', 分类ID: ' . $cat_id);
        echo 0;
        wp_die();
    }
    
    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    // 获取当前分类访问时间数据
    $cat_view_time = get_user_meta($user_id, 'cat_view_time', true);
    $cat_view_time = $cat_view_time ? $cat_view_time : "";
    
    // 解析现有数据
    $cat_view_array = array();
    if (!empty($cat_view_time)) {
        $cat_view_array = json_decode($cat_view_time, true);
        if (!is_array($cat_view_array)) {
            $cat_view_array = array();
        }
    }
    
    // 添加新的访问记录
    $current_time = current_time("timestamp");
    $cat_view_array[] = array(
        'cat_id' => $cat_id,
        'visit_time' => $current_time
    );
    
    // 保存更新后的数据
    $new_cat_view_time = json_encode($cat_view_array);
    $result = update_user_meta($user_id, 'cat_view_time', $new_cat_view_time);
    
    error_log('[LearningReviewDebugger] 分类访问记录更新结果: ' . ($result ? '成功' : '失败'));
    error_log('[LearningReviewDebugger] 新增访问记录 - 分类ID: ' . $cat_id . ', 时间: ' . date('Y-m-d H:i:s', $current_time));
    
    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_record_category_visit', 'lrd_admin_record_category_visit');
error_log('[LearningReviewDebugger] admin_record_category_visit AJAX action已注册');

/**
 * 重置分类访问时间的AJAX处理函数
 */
function lrd_admin_reset_category_visit() {
    error_log('[LearningReviewDebugger] lrd_admin_reset_category_visit AJAX请求开始处理');
    
    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }
    
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置分类访问时间 - 用户ID: ' . $user_id);
    
    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }
    
    // 重置分类访问时间为空
    $result = update_user_meta($user_id, 'cat_view_time', '');
    
    error_log('[LearningReviewDebugger] 分类访问时间重置结果: ' . ($result ? '成功' : '失败'));
    
    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_reset_category_visit', 'lrd_admin_reset_category_visit');
error_log('[LearningReviewDebugger] admin_reset_category_visit AJAX action已注册');

/**
 * 获取用户复习信息的AJAX处理函数
 */
function lrd_admin_review_get_user_review_mes() {
    error_log('[LearningReviewDebugger] lrd_admin_review_get_user_review_mes AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 查询用户复习信息 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取复习信息
    $review_mes = get_user_meta($user_id, 'review_mes', true);
    $review_mes = $review_mes ? $review_mes : "";

    error_log('[LearningReviewDebugger] 复习信息查询完成，数据长度: ' . strlen($review_mes));

    echo $review_mes;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_get_user_review_mes', 'lrd_admin_review_get_user_review_mes');
error_log('[LearningReviewDebugger] admin_review_get_user_review_mes AJAX action已注册');

/**
 * 重置复习时间的AJAX处理函数
 */
function lrd_reset_alter_review_time() {
    error_log('[LearningReviewDebugger] lrd_reset_alter_review_time AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置复习时间 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置复习时间为0
    $result = update_user_meta($user_id, 'alter_review_time', 0);

    error_log('[LearningReviewDebugger] 复习时间重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_alter_review_time', 'lrd_reset_alter_review_time');
error_log('[LearningReviewDebugger] reset_alter_review_time AJAX action已注册');

/**
 * 重置黑卡数据时间的AJAX处理函数
 */
function lrd_reset_alter_heika_data_time() {
    error_log('[LearningReviewDebugger] lrd_reset_alter_heika_data_time AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置黑卡数据时间 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置黑卡数据时间为0
    $result = update_user_meta($user_id, 'alter_heika_data_time', 0);

    error_log('[LearningReviewDebugger] 黑卡数据时间重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_alter_heika_data_time', 'lrd_reset_alter_heika_data_time');
error_log('[LearningReviewDebugger] reset_alter_heika_data_time AJAX action已注册');

/**
 * 重置用户学习详情的AJAX处理函数
 */
function lrd_reset_current_user_learn_detail() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_learn_detail AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户学习详情 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置学习详情为空 (修正字段名)
    $result = update_user_meta($user_id, 'learn_detail', '');

    error_log('[LearningReviewDebugger] 用户学习详情重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_learn_detail', 'lrd_reset_current_user_learn_detail');
error_log('[LearningReviewDebugger] reset_current_user_learn_detail AJAX action已注册');

/**
 * 重置用户今日学习详情的AJAX处理函数
 */
function lrd_reset_current_user_today_learn_detail() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_today_learn_detail AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户今日学习详情 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置今日学习详情为空 (修正字段名)
    $result = update_user_meta($user_id, 'today_learn_detail', '');

    error_log('[LearningReviewDebugger] 用户今日学习详情重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_today_learn_detail', 'lrd_reset_current_user_today_learn_detail');
error_log('[LearningReviewDebugger] reset_current_user_today_learn_detail AJAX action已注册');

/**
 * 重置用户黑卡打卡详情的AJAX处理函数
 */
function lrd_reset_current_user_heika_check_in_detail() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_heika_check_in_detail AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户黑卡打卡详情 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置黑卡打卡详情为空
    $result = update_user_meta($user_id, 'heika_check_in_detail', '');

    error_log('[LearningReviewDebugger] 用户黑卡打卡详情重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_heika_check_in_detail', 'lrd_reset_current_user_heika_check_in_detail');
error_log('[LearningReviewDebugger] reset_current_user_heika_check_in_detail AJAX action已注册');

/**
 * 重置用户黑卡日常学习任务内容的AJAX处理函数
 */
function lrd_reset_current_user_heika_daily_learning_task_content() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_heika_daily_learning_task_content AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户黑卡日常学习任务内容 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置黑卡日常学习任务内容为空
    $result = update_user_meta($user_id, 'heika_daily_learning_task_content', '');

    error_log('[LearningReviewDebugger] 用户黑卡日常学习任务内容重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_heika_daily_learning_task_content', 'lrd_reset_current_user_heika_daily_learning_task_content');
error_log('[LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content AJAX action已注册');

/**
 * 重置用户黑卡日常学习任务总数据的AJAX处理函数
 */
function lrd_reset_current_user_heika_daily_learning_task_content_total() {
    error_log('[LearningReviewDebugger] lrd_reset_current_user_heika_daily_learning_task_content_total AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户黑卡日常学习任务总数据 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置黑卡日常学习任务总数据为空
    $result = update_user_meta($user_id, 'heika_daily_learning_task_content_total', '');

    error_log('[LearningReviewDebugger] 用户黑卡日常学习任务总数据重置结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_reset_current_user_heika_daily_learning_task_content_total', 'lrd_reset_current_user_heika_daily_learning_task_content_total');
error_log('[LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content_total AJAX action已注册');

/**
 * 清除复习信息的AJAX处理函数
 */
function lrd_admin_review_clear_review_mes() {
    error_log('[LearningReviewDebugger] lrd_admin_review_clear_review_mes AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 清除复习信息 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 清除复习信息
    $result = update_user_meta($user_id, 'review_mes', '');

    error_log('[LearningReviewDebugger] 复习信息清除结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_clear_review_mes', 'lrd_admin_review_clear_review_mes');
error_log('[LearningReviewDebugger] admin_review_clear_review_mes AJAX action已注册');

/**
 * 获取当前时间戳的AJAX处理函数
 */
function lrd_get_current_timestamp() {
    error_log('[LearningReviewDebugger] lrd_get_current_timestamp AJAX请求开始处理');

    // 使用WordPress的current_time函数获取当前时间戳
    $current_timestamp = current_time("timestamp");

    error_log('[LearningReviewDebugger] 当前时间戳: ' . $current_timestamp . ' (' . date('Y-m-d H:i:s', $current_timestamp) . ')');

    // 返回时间戳
    echo $current_timestamp;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_get_current_timestamp', 'lrd_get_current_timestamp');
add_action('wp_ajax_nopriv_get_current_timestamp', 'lrd_get_current_timestamp');
error_log('[LearningReviewDebugger] get_current_timestamp AJAX action已注册');

/**
 * 清理用户所有数据的AJAX处理函数
 */
function lrd_admin_review_clean_user_all_data() {
    error_log('[LearningReviewDebugger] lrd_admin_review_clean_user_all_data AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 清理用户所有数据 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 定义要清理的用户元数据字段 (修正字段名)
    $meta_keys_to_clean = array(
        'review_mes',
        'course_rate_of_progress',
        'learn_detail',
        'today_learn_detail',
        'heika_check_in_detail',
        'heika_daily_learning_task_content',
        'heika_daily_learning_task_content_total',
        'alter_review_time',
        'alter_heika_data_time',
        'cat_view_time'
    );

    $cleaned_count = 0;

    // 逐个清理元数据
    foreach ($meta_keys_to_clean as $meta_key) {
        $result = update_user_meta($user_id, $meta_key, '');
        if ($result) {
            $cleaned_count++;
            error_log('[LearningReviewDebugger] 清理字段成功: ' . $meta_key);
        } else {
            error_log('[LearningReviewDebugger] 清理字段失败: ' . $meta_key);
        }
    }

    error_log('[LearningReviewDebugger] 用户数据清理完成 - 成功清理字段数: ' . $cleaned_count . '/' . count($meta_keys_to_clean));

    echo $cleaned_count > 0 ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_clean_user_all_data', 'lrd_admin_review_clean_user_all_data');
add_action('wp_ajax_nopriv_admin_review_clean_user_all_data', 'lrd_admin_review_clean_user_all_data');
error_log('[LearningReviewDebugger] admin_review_clean_user_all_data AJAX action已注册');

/**
 * 重置用户复习信息和课程进度的AJAX处理函数
 */
function lrd_admin_reset_user_review_mes_and_course_rate_of_progress() {
    error_log('[LearningReviewDebugger] lrd_admin_reset_user_review_mes_and_course_rate_of_progress AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 重置用户复习信息和课程进度 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 重置复习信息和课程进度
    $result1 = update_user_meta($user_id, 'review_mes', '');
    $result2 = update_user_meta($user_id, 'course_rate_of_progress', '');

    $success = $result1 && $result2;
    error_log('[LearningReviewDebugger] 复习信息重置: ' . ($result1 ? '成功' : '失败'));
    error_log('[LearningReviewDebugger] 课程进度重置: ' . ($result2 ? '成功' : '失败'));

    echo $success ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_reset_user_review_mes_and_course_rate_of_progress', 'lrd_admin_reset_user_review_mes_and_course_rate_of_progress');
error_log('[LearningReviewDebugger] admin_reset_user_review_mes_and_course_rate_of_progress AJAX action已注册');

/**
 * 创建测试复习信息的AJAX处理函数
 */
function lrd_admin_review_create_test_review_mes() {
    error_log('[LearningReviewDebugger] lrd_admin_review_create_test_review_mes AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        echo 0;
        wp_die();
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    error_log('[LearningReviewDebugger] 创建测试复习信息 - 用户ID: ' . $user_id);

    // 验证参数
    if ($user_id <= 0) {
        error_log('[LearningReviewDebugger] 用户ID无效: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        echo 0;
        wp_die();
    }

    // 创建符合reviewMesHandle格式的测试复习数据
    $today_morning = strtotime(current_time("Y-m-d"));
    $test_review_data = array(
        array(
            'post_id' => 1,
            'finishTimes' => 1,
            'model' => 43, // 默认分类ID
            'reviewTime' => $today_morning + 86400, // 明天复习
            'word_arr' => array(),
            'sentence_arr' => array()
        ),
        array(
            'post_id' => 2,
            'finishTimes' => 1,
            'model' => 43, // 默认分类ID
            'reviewTime' => $today_morning + 172800, // 后天复习
            'word_arr' => array(),
            'sentence_arr' => array()
        )
    );

    // 保存测试复习数据
    $result = update_user_meta($user_id, 'review_mes', json_encode($test_review_data));

    error_log('[LearningReviewDebugger] 测试复习信息创建结果: ' . ($result ? '成功' : '失败'));

    echo $result ? 1 : 0;
    wp_die();
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_review_create_test_review_mes', 'lrd_admin_review_create_test_review_mes');
error_log('[LearningReviewDebugger] admin_review_create_test_review_mes AJAX action已注册');

/**
 * 删除单个课程进度的AJAX处理函数
 */
function lrd_admin_delete_single_course_progress() {
    error_log('[LearningReviewDebugger] lrd_admin_delete_single_course_progress AJAX请求开始处理');

    // 验证用户权限
    if (!current_user_can('manage_options')) {
        error_log('[LearningReviewDebugger] 权限验证失败 - 用户无管理员权限');
        wp_send_json_error(['message' => '权限不足']);
        return;
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

    error_log('[LearningReviewDebugger] 删除单个课程进度 - 用户ID: ' . $user_id . ', 课程ID: ' . $post_id);

    // 验证参数
    if ($user_id <= 0 || $post_id <= 0) {
        error_log('[LearningReviewDebugger] 参数验证失败 - 用户ID: ' . $user_id . ', 课程ID: ' . $post_id);
        wp_send_json_error(['message' => '参数无效']);
        return;
    }

    // 获取用户信息
    $user = get_user_by('id', $user_id);
    if (!$user) {
        error_log('[LearningReviewDebugger] 用户不存在: ' . $user_id);
        wp_send_json_error(['message' => '用户不存在']);
        return;
    }

    // 获取课程进度数据
    $course_progress = get_user_meta($user_id, 'course_rate_of_progress', true);
    if (empty($course_progress)) {
        error_log('[LearningReviewDebugger] 用户无课程进度数据');
        wp_send_json_error(['message' => '无课程进度数据']);
        return;
    }

    // 解析JSON数据
    $progress_array = json_decode($course_progress, true);
    if (!is_array($progress_array)) {
        error_log('[LearningReviewDebugger] 课程进度数据格式错误');
        wp_send_json_error(['message' => '数据格式错误']);
        return;
    }

    // 查找并删除指定课程的进度
    $found = false;
    foreach ($progress_array as $key => $progress_item) {
        if (isset($progress_item['post_id']) && $progress_item['post_id'] == $post_id) {
            unset($progress_array[$key]);
            $found = true;
            error_log('[LearningReviewDebugger] 找到并删除课程进度 - 课程ID: ' . $post_id);
            break;
        }
    }

    if (!$found) {
        error_log('[LearningReviewDebugger] 未找到指定课程的进度数据 - 课程ID: ' . $post_id);
        wp_send_json_error(['message' => '未找到指定课程进度']);
        return;
    }

    // 重新索引数组并保存
    $progress_array = array_values($progress_array);
    $new_progress_json = json_encode($progress_array);

    $result = update_user_meta($user_id, 'course_rate_of_progress', $new_progress_json);

    error_log('[LearningReviewDebugger] 课程进度删除结果: ' . ($result ? '成功' : '失败'));

    if ($result) {
        wp_send_json_success(['message' => '课程进度删除成功']);
    } else {
        wp_send_json_error(['message' => '删除失败']);
    }
}
// 注册AJAX处理函数
add_action('wp_ajax_admin_delete_single_course_progress', 'lrd_admin_delete_single_course_progress');
error_log('[LearningReviewDebugger] admin_delete_single_course_progress AJAX action已注册');

error_log('[LearningReviewDebugger] AJAX处理器完整加载完成 - 所有核心函数已迁移');

?>

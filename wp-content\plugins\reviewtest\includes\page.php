<script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js" type="text/javascript"></script>
<script src="<?php bloginfo('template_url'); ?>/js/jquery-1.11.3.min.js" type="text/javascript"></script>

<?php
/*$todaymorning = strtotime(current_time("Y-m-d H:i"));//当天0点
$todaynight = $todaymorning + 24 * 3600 - 1;//当天23.59
$lastdaynight = $todaymorning - 1;//昨天晚间23.59
$lastdaymorning = $lastdaynight - 24 * 3600 + 1;//昨天0点
$tomorrowmorning = $todaymorning + 24 * 3600;//明天0点

$review_mes=get_user_meta(1,"review_mes",true);
$review_mes_arr=json_decode($review_mes);*/
//myDump($review_mes_arr);


$ajax_url = admin_url("admin-ajax.php");


//myDump("现在：" . date("Y.m.d  H:i", 1571356944));


$simulated_time = current_time("timestamp");
$today_morning = strtotime(current_time("Y-m-d"));


$webUrl = home_url();//网站域名


$course_review_ajax_url = $webUrl . "/?page_id=72630";//记忆系统测试地址


$args = array(
    'numberposts' => 20,
    'category' => 20,
    'orderby' => 'post_date',
    'order' => 'ASC',
    'post_type' => 'post',
    'post_status' => 'publish');


$postObjectArr = get_posts($args);


//myDump($postObjectArr);

$id_arr = array();

for ($i = 0; $i < count($postObjectArr); $i++) {
    array_push($id_arr, $postObjectArr[$i]->ID);
}


//myDump($id_arr);


?>

<style>

    .amts .form-table th, .amts .form-table td {
        border: none !important;
    }

    .list {
        margin-bottom: 10px;
    }

    .list div, h2 {
        width: 100%;

        text-align: left;
        display: block;
        margin-bottom: 20px;
        float: left;

    }

    .list-title {
        width: 100%;
        text-align: center;
        border-bottom: 1px solid darkgray;
        float: left;
        margin-top: 5px;
        height: 20px;
        line-height: 20px;

    }

    .list-title div {
        width: 20%;
        float: left;
        display: block;
        min-height: 30px;
        text-align: center;
        border: 1px;
    }

    .small_table {

        border: 1px solid #5cb85c;
        float: left;
        margin-top: 10px;
    }

    .small_table div {
        width: 50%;
        float: left;
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin: 0px;

    }

    .show_area {
        width: 100%;
        height: auto;
        background: white;
        padding: 4px;

    }

    .show_area span {
        float: left;
        background: limegreen;
        padding: 4px;
        color: white;
        font-size: 12px;
        border-radius: 5px;
        margin: 2px;
    }


</style>

<div class="wrap amts" id="container">


    <div class="list" v-show="!current_user_id">

        <p>输入用户ID：</p>

        <input v-model="search_user_id" type="text"/>

        <button @click="search_user">搜索</button>

    </div>


    <div class="list" v-show="current_user_id">


        <h2>当前操作用户</h2>


        <div>
            ID：{{current_user_id}}
        </div>

        <div>
            状态：<span v-show="isHeikaVip==1" style="color: red">黑卡会员开启中</span><span v-show="isHeikaVip!=1"
                                                                                   style="color: darkgray">黑卡会员关闭</span>
            <button @click="change_isHeikaVip">切换</button>
        </div>


        <div>
            复习计划更新时间（alter_review_time）：{{current_user_alter_review_time}}
            <button v-show="current_user_alter_review_time" @click="reset_alter_review_time">时间重置</button>
        </div>

        <div>
            每日任务更新时间（alter_heika_data_time）：{{current_user_alter_heika_data_time}}
            <button v-show="current_user_alter_heika_data_time" @click="reset_alter_heika_data_time">时间重置</button>
        </div>


        <div>
            每日任务内容（heika_daily_learning_task_content）：{{current_user_heika_daily_learning_task_content}}
            <button v-show="current_user_heika_daily_learning_task_content"
                    @click="reset_current_user_heika_daily_learning_task_content">清空
            </button>
        </div>


        <div>
            历史任务内容（heika_daily_learning_task_content_total）：
            <button v-show="current_user_heika_daily_learning_task_content_total"
                    @click="reset_current_user_heika_daily_learning_task_content_total">清空
            </button>

            <div class="show_area">


                <template v-for="heika_task in current_user_heika_daily_learning_task_content_total">


                      <span v-if="heika_task.heika_daily_learning_task_content=='NOT_CREATE'" style="background: red">

                          {{get_YMI(heika_task.day_timestamp)}}&nbsp;&nbsp;{{heika_task.heika_daily_learning_task_content}}

                       </span>


                    <span v-if="heika_task.heika_daily_learning_task_content!='NOT_CREATE'">

                          {{get_YMI(heika_task.day_timestamp)}}&nbsp;&nbsp;{{heika_task.heika_daily_learning_task_content}}

                    </span>


                </template>
            </div>
        </div>


        <div>
            每日打卡详情（heika_check_in_detail）：
            <button v-show="current_user_heika_check_in_detail" @click="reset_current_user_heika_check_in_detail">清空
            </button>

            <div class="show_area">


                <template v-for="check_in_detail in current_user_heika_check_in_detail">


                    <span v-if="check_in_detail.is_finish_heika_daily_learning_task==0" style="background: red">

                        {{get_YMI(check_in_detail.day_timestamp)}}&nbsp;&nbsp;{{(check_in_detail.is_finish_heika_daily_learning_task==0)?"未打卡":"已打卡"}}

                    </span>


                     <span v-if="check_in_detail.is_finish_heika_daily_learning_task==1">

                        {{get_YMI(check_in_detail.day_timestamp)}}&nbsp;&nbsp;{{(check_in_detail.is_finish_heika_daily_learning_task==0)?"未打卡":"已打卡"}}

                    </span>

                </template>

            </div>


        </div>

        <div>
            总共统计时间（天）：{{current_user_heika_check_in_detail.length}}
        </div>


        <div>
            连续打卡（天）：{{current_user_continuously_learning_days}}
        </div>
        <div>
            110天内累积打卡（天）：{{current_user_total_learning_days_in_110}}
        </div>


        <div>
            学习行为（learn_detail）
            <button v-show="current_user_learn_detail" @click="reset_current_user_learn_detail">清空</button>

            <div class="show_area">


              <span v-for="learn_detail in current_user_learn_detail">

                  {{get_YMI(learn_detail.timestamp)}}
                  &nbsp;&nbsp;
                  {{(learn_detail.course_status==0)?"学":"复"}}
                  &nbsp;&nbsp;
                  {{learn_detail.post_id}}


            </span>

            </div>
        </div>

        <div>
            今日学习行为（today_learn_detail）
            <button v-show="current_user_today_learn_detail" @click="reset_current_user_today_learn_detail">清空</button>

            <div class="show_area">

          <span v-for="learn_detail in current_user_today_learn_detail">

                  {{get_YMI(learn_detail.timestamp)}}
                  &nbsp;&nbsp;
                  {{(learn_detail.course_status==0)?"学习":"复习"}}
                  &nbsp;&nbsp;
                  {{learn_detail.post_id}}


            </span>
            </div>


        </div>
        <div>
            复习溢出数（today_review_over_num）：{{current_user_today_review_over_num}}
        </div>
    </div>


    <div class="list" v-show="current_user_id">

        <h2 v-show="current_user_overdueReviewArr.length>0">过期复习</h2>

        <div v-show="current_user_overdueReviewArr.length>0" class='list-title'>
            <div>课程ID</div>
            <div>完成次数</div>
            <div>类目</div>
            <div>下次复习时间</div>

        </div>


        <div class='list-title' v-for="(overdueReviewArr,key) in current_user_overdueReviewArr">


            <div>{{overdueReviewArr.post_id}}</div>
            <div>{{overdueReviewArr.finishTimes}}</div>
            <div>{{overdueReviewArr.model}}</div>
            <div>{{reviewTime_YMI(overdueReviewArr.reviewTime)}}</div>

        </div>


        <h2 v-show="current_user_todayReviewArr.length>0">今日复习</h2>

        <div v-show="current_user_todayReviewArr.length>0" class='list-title'>
            <div>课程ID</div>
            <div>完成次数</div>
            <div>类目</div>
            <div>下次复习时间</div>
            <div>操作</div>
        </div>


        <div class='list-title' v-for="(todayReviewArr,key) in current_user_todayReviewArr">


            <div>{{todayReviewArr.post_id}}</div>
            <div>{{todayReviewArr.finishTimes}}</div>
            <div>{{todayReviewArr.model}}</div>
            <div>{{reviewTime_YMI(todayReviewArr.reviewTime)}}</div>
            <div>
                <button @click="finish_review(todayReviewArr.post_id,todayReviewArr.model)">完成它</button>
            </div>

        </div>


        <h2 v-show="current_user_notTodayReviewArr.length>0">复习中</h2>

        <div v-show="current_user_notTodayReviewArr.length>0" class='list-title'>
            <div>课程ID</div>
            <div>完成次数</div>
            <div>类目</div>
            <div>下次复习时间</div>

        </div>

        <div class='list-title' v-for="(notTodayReviewArr,key) in current_user_notTodayReviewArr">

            <div>{{notTodayReviewArr.post_id}}</div>
            <div>{{notTodayReviewArr.finishTimes}}</div>
            <div>{{notTodayReviewArr.model}}</div>
            <div>{{reviewTime_YMI(notTodayReviewArr.reviewTime)}}</div>
        </div>


        <h2 v-show="current_user_finishReviewArr.length>0">已完成</h2>

        <div v-show="current_user_finishReviewArr.length>0" class='list-title'>
            <div>课程ID</div>
            <div>完成次数</div>
            <div>类目</div>
            <div>下次复习时间</div>
        </div>


        <div class='list-title' v-for="(finishReviewArr,key) in current_user_finishReviewArr">

            <div>{{finishReviewArr.post_id}}</div>
            <div>{{finishReviewArr.finishTimes}}</div>
            <div>{{finishReviewArr.model}}</div>
            <div>{{reviewTime_YMI(finishReviewArr.reviewTime)}}</div>
        </div>


    </div>


    <div class="list" v-show="current_user_id">

        <h2>模拟登录</h2>

        <div style="color:red">模式时间：{{get_YMI(simulated_time)}}</div>
        <div>
            <button @click="simulated_user_login">模拟登录</button>
            <button @click="add_a_day">时间加一天</button>
            <button @click="learn_one">学一节新课</button>
        </div>
        <h2>操作</h2>

        <div>
            <button @click="random_create_review">为该用户随机生成复习计划</button>
            <button @click="clear_review">清除其复习计划</button>
            <button @click="reset_user">重置用户所有内容</button>


            <button @click="quit_test">退出测试</button>
        </div>
    </div>


</div>


<script>

    var container = new Vue({
        el: '#container',
        data: {

            isHeikaVip: 0,

            can_learn_ids:<?php echo json_encode($id_arr)?>,

            simulated_time:<?php echo $simulated_time;?>,

            today_morning:<?php echo $today_morning;?>,

            today_night: 0,
            yesterday_night: 0,
            yesterday_morning: 0,
            tomorrow_morning: 0,


            course_review_ajax_url: "<?php echo $course_review_ajax_url;?>",

            ajax_url: "<?php echo $ajax_url;?>",
            search_user_id: "",


            current_user_id: "",
            current_user_alter_review_time: "",
            current_user_alter_heika_data_time: "",

            current_user_heika_daily_learning_task_content: "",
            current_user_heika_daily_learning_task_content_total: "",

            current_user_heika_check_in_detail: "",
            current_user_continuously_learning_days: 0,
            current_user_total_learning_days_in_110: 0,

            current_user_heika_task_text: "",

            current_user_learn_detail: "",
            current_user_today_learn_detail: "",
            current_user_today_review_over_num: "",

            current_user_review_mes_arr: [],
            current_user_overdueReviewArr: [],
            current_user_todayReviewArr: [],
            current_user_notTodayReviewArr: [],
            current_user_finishReviewArr: []


        },
        methods: {

            change_isHeikaVip: function () {
                if (this.isHeikaVip == 0) {
                    this.isHeikaVip = 1;

                } else {
                    this.isHeikaVip = 0;
                }
            },

            ori_time_go: function () {

                this.today_night = this.today_morning + 24 * 3600 - 1;//当天23.59
                this.yesterday_night = this.today_morning - 1;//昨天晚间23.59
                this.yesterday_morning = this.yesterday_night - 24 * 3600 + 1;//昨天0点
                this.tomorrow_morning = this.today_morning + 24 * 3600;//明天0点
            },


            search_user: function () {



                //alert(parseInt(this.search_user_id));

                if (!parseInt(this.search_user_id)) {
                    alert("只能搜索数字");
                    this.search_user_id = "";

                } else {


                    this.current_user_id = this.search_user_id;

                    this.data_reset();
                    this.fill_user_data();
                    this.fill_user_review_mes();

                }


            },


            fill_user_data: function () {

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'admin_review_get_user_data',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {
                            //当结果不等于0
                            res = res.substring(0, res.length - 1);//去掉最后个字符0
                            //console.log(res);


                            res = JSON.parse(res);//转为json对象
                            //console.log(res.user_nickname);


                            _this.current_user_alter_review_time = (res.alter_review_time) ? _this.get_YMI(res.alter_review_time, "Y.m.d  H:i") : "";
                            _this.current_user_alter_heika_data_time = (res.alter_heika_data_time) ? _this.get_YMI(res.alter_heika_data_time, "Y.m.d H:i") : "";

                            _this.current_user_heika_daily_learning_task_content = res.heika_daily_learning_task_content;


                            _this.current_user_heika_daily_learning_task_content_total = (res.heika_daily_learning_task_content_total) ? JSON.parse(res.heika_daily_learning_task_content_total) : "";


                            _this.current_user_heika_check_in_detail = (res.heika_check_in_detail) ? JSON.parse(res.heika_check_in_detail) : "";


                            _this.current_user_continuously_learning_days = res.continuously_learning_days;

                            _this.current_user_total_learning_days_in_110 = res.total_learning_days_in_110;


                            _this.current_user_learn_detail = (res.learn_detail) ? JSON.parse(res.learn_detail) : "";


                            _this.current_user_today_learn_detail = (res.today_learn_detail) ? JSON.parse(res.today_learn_detail) : "";


                            _this.current_user_today_review_over_num = (res.today_review_over_num) ? res.today_review_over_num : "";


                        } else {
                            alert("没有这个用户");
                        }


                    }
                });

            },


            fill_user_review_mes: function () {
                var _this = this;


                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'admin_review_get_user_review_mes',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {
                            //当结果不等于0
                            res = res.substring(0, res.length - 1);//去掉最后个字符0
                            //console.log(res);


                            res = JSON.parse(res);//转为json对象
                            //console.log(res);


                            /*
                             * 复习计划数组赋值
                             *
                             * */

                            _this.current_user_review_mes_arr = res;

                            for (var i = 0; i < _this.current_user_review_mes_arr.length; i++) {


                                if (_this.current_user_review_mes_arr[i].reviewTime == 0) {

                                    _this.current_user_finishReviewArr.push(_this.current_user_review_mes_arr[i]);//已完成

                                } else {

                                    if (_this.current_user_review_mes_arr[i].reviewTime < _this.today_morning) {

                                        _this.current_user_overdueReviewArr.push(_this.current_user_review_mes_arr[i]);//过期

                                    }


                                    if (_this.current_user_review_mes_arr[i].reviewTime >= _this.today_morning && _this.current_user_review_mes_arr[i].reviewTime <= _this.today_night) {

                                        _this.current_user_todayReviewArr.push(_this.current_user_review_mes_arr[i]);//今天

                                    }


                                    if (_this.current_user_review_mes_arr[i].reviewTime >= _this.tomorrow_morning) {

                                        _this.current_user_notTodayReviewArr.push(_this.current_user_review_mes_arr[i]);//未来

                                    }


                                }


                            }
                        }


                    }
                });


            },

            reset_alter_review_time: function () {

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'reset_alter_review_time',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {

                            alert("重置成功");

                        } else {
                            alert("重置失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });

            },

            reset_alter_heika_data_time: function () {

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'reset_alter_heika_data_time',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {

                            alert("重置成功");

                        } else {
                            alert("重置失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });

            },


            reset_current_user_learn_detail: function () {

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'reset_current_user_learn_detail',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {

                            alert("重置成功");

                        } else {
                            alert("重置失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });

            },


            reset_current_user_today_learn_detail: function () {

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'reset_current_user_today_learn_detail',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {

                            alert("重置成功");

                        } else {
                            alert("重置失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });

            },

            reset_current_user_heika_check_in_detail: function () {

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'reset_current_user_heika_check_in_detail',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {

                            alert("重置成功");

                        } else {
                            alert("重置失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });

            },

            reset_current_user_heika_daily_learning_task_content: function () {


                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'reset_current_user_heika_daily_learning_task_content',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {

                            alert("重置成功");

                        } else {
                            alert("重置失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });

            },


            reset_current_user_heika_daily_learning_task_content_total: function () {


                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'reset_current_user_heika_daily_learning_task_content_total',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {

                            alert("重置成功");

                        } else {
                            alert("重置失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });

            },


            reset_user: function () {

                if (confirm("重置所有内容，包括复习计划，进度等等，日常任务将重新生成")) {

                    var _this = this;

                    $.ajax({
                        url: _this.ajax_url,
                        type: 'post',
                        data: {
                            action: 'admin_review_clean_user_all_data',
                            user_id: _this.current_user_id
                        },
                        success: function (data, status) {
                            //将结果转为字符串，去掉收尾空格

                            //console.log(data);
                            var res = data.toString().trim();

                            if (res != 0) {

                                alert("重置成功");

                            } else {
                                alert("重置失败");
                            }

                            _this.data_reset();
                            _this.fill_user_data();
                            _this.fill_user_review_mes();


                        }
                    });

                }


            },


            get_YMI: function (number, format) {

                var time_millisecond_in_js = parseInt(number) * 1000 - (8 * 3600 * 1000);//今天早上毫秒数

                var d = new Date();//日期对象

                d.setTime(time_millisecond_in_js);//设置今天时间
                var y = d.getFullYear();//今天星期几
                var m = d.getMonth() + 1;
                var i = d.getDate();
                var h = d.getHours();
                var mi = d.getMinutes();
                var s = d.getSeconds();


                return y + "-" + m + "-" + i + " " + h + ":" + mi + ":" + s;
            },

            formatNumber: function (n) {
                n = n.toString();
                return n[1] ? n : '0' + n
            },


            reviewTime_YMI: function (timestamp) {

                if (timestamp) {

                    return this.get_YMI(timestamp, "Y.m.d  H:i");

                } else {

                    return 0;

                }
            },


            random_create_review: function () {
                //alert("随机生成复习计划");

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'admin_review_create_test_review_mes',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {
                            //当结果不等于0
                            res = res.substring(0, res.length - 1);//去掉最后个字符0
                            alert("成功");


                        } else {
                            alert("失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });


            },

            clear_review: function () {
                //alert("清除所有复习");

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'admin_review_clear_review_mes',
                        user_id: _this.current_user_id
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        //console.log(data);
                        var res = data.toString().trim();

                        if (res != 0) {
                            //当结果不等于0
                            alert("删除成功");

                        } else {
                            alert("删除失败");
                        }

                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });


            },

            finish_review: function (post_id, model) {
                //alert(post_id);
                //alert(model);


                var _this = this;

                /*
                 * 先保存记忆系统内容
                 * */


                $.ajax({
                    url: _this.course_review_ajax_url,
                    type: 'post',
                    data: {
                        action: 1,
                        user_id: _this.current_user_id,
                        course_status: 1,
                        isHeikaVip: _this.isHeikaVip,
                        model: model,
                        post_id: post_id,
                        nowTime: _this.simulated_time
                    },
                    success: function (data, status) {


                        var res = JSON.parse(data);//转为json对象

                        console.log(res);

                        if (res.is_finish_heika_daily_learning_task == 1) {
                            //已完成任务

                            if (res.is_inform_user == 1) {
                                alert("今日任务已完成");
                            }


                        } else {
                            var last = 0;
                            switch (res.heika_daily_learning_task_content) {
                                case "LEARN_TWO":


                                    last = 2 - res.learn_num;

                                    //alert("还需再学习"+last+"课，即可完成今日任务。");

                                    alert("今日任务进度：学习课数(" + res.learn_num + "/2) ");


                                    break;
                                case "LEARN_ONE_REVIEW_ONE":

                                    if (res.learn_num >= 1) {


                                        alert("今日任务进度：学习课数(1/1)  复习课数（0/1）");


                                    }

                                    if (res.review_num >= 1) {


                                        alert("今日任务进度：学习课数(0/1)  复习课数（1/1）");

                                    }


                                    break;
                                case "FINISH_ALL_REVIEWING_CONTENT":

                                    last = res.last_today_review;


                                    alert("今日任务进度：还需复习" + last + "课");


                                    break;

                            }


                        }


                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });


            },

            learn_one: function () {

                var _this = this;

                // console.log(this.can_learn_ids);
                //console.log(this.current_user_review_mes_arr);

                var learn_one_id = '';


                for (var i = 0; i < this.can_learn_ids.length; i++) {

                    var is_can_learn = true;

                    for (var j = 0; j < this.current_user_review_mes_arr.length; j++) {

                        //console.log(this.current_user_review_mes_arr[j].post_id);

                        if (this.current_user_review_mes_arr[j].post_id == this.can_learn_ids[i]) {
                            is_can_learn = false;
                        }

                    }

                    if (is_can_learn) {
                        learn_one_id = this.can_learn_ids[i];
                        break;
                    }
                }

                console.log(learn_one_id);


                if (learn_one_id) {


                    $.ajax({
                        url: _this.course_review_ajax_url,
                        type: 'post',
                        data: {
                            action: 1,
                            user_id: _this.current_user_id,
                            course_status: 0,
                            isHeikaVip: _this.isHeikaVip,
                            model: 20,
                            post_id: learn_one_id,
                            nowTime: _this.simulated_time
                        },
                        success: function (data, status) {


                            var res = JSON.parse(data);//转为json对象

                            console.log(res);

                            if (res.is_finish_heika_daily_learning_task == 1) {
                                //已完成任务

                                if (res.is_inform_user == 1) {
                                    alert("今日任务已完成");
                                }


                            } else {
                                var last = 0;
                                switch (res.heika_daily_learning_task_content) {
                                    case "LEARN_TWO":


                                        last = 2 - res.learn_num;

                                        //alert("还需再学习"+last+"课，即可完成今日任务。");

                                        alert("今日任务进度：学习课数(" + res.learn_num + "/2) ");


                                        break;
                                    case "LEARN_ONE_REVIEW_ONE":

                                        if (res.learn_num >= 1) {


                                            alert("今日任务进度：学习课数(1/1)  复习课数（0/1）");


                                        }

                                        if (res.review_num >= 1) {


                                            alert("今日任务进度：学习课数(0/1)  复习课数（1/1）");

                                        }


                                        break;
                                    case "FINISH_ALL_REVIEWING_CONTENT":

                                        last = res.last_today_review;


                                        alert("今日任务进度：还需复习" + last + "课");


                                        break;

                                }


                            }


                            _this.data_reset();
                            _this.fill_user_data();
                            _this.fill_user_review_mes();


                        }
                    });


                } else {
                    alert("没有可学习的课程");
                }


            },


            simulated_user_login: function () {
                //alert("现在模拟登录");

                var _this = this;

                $.ajax({
                    url: _this.ajax_url,
                    type: 'post',
                    data: {
                        action: 'admin_review_simulated_user_login',
                        user_id: _this.current_user_id,
                        simulated_time: _this.simulated_time,
                        isHeikaVip: _this.isHeikaVip
                    },
                    success: function (data, status) {
                        //将结果转为字符串，去掉收尾空格

                        console.log(data);


                        _this.data_reset();
                        _this.fill_user_data();
                        _this.fill_user_review_mes();


                    }
                });
            },

            add_a_day: function () {

                this.simulated_time += 24 * 3600;

                console.log("模拟时间：" + this.get_YMI(this.simulated_time));

                this.today_morning += 24 * 3600;


                console.log("今早：" + this.get_YMI(this.today_morning));


            },


            add_a_hour: function () {

                this.simulated_time += 3600;

                console.log("模拟时间：" + this.get_YMI(this.simulated_time));

                this.today_morning += 3600;


                console.log("今早：" + this.get_YMI(this.today_morning));


            },


            quit_test: function () {
                this.current_user_id = "";
                this.search_user_id = "";
                this.data_reset();
            },


            data_reset: function () {

                this.current_user_alter_review_time = "";
                this.current_user_alter_heika_data_time = "";

                this.current_user_heika_daily_learning_task_content = "";
                this.current_user_heika_daily_learning_task_content_total = "";
                this.current_user_heika_check_in_detail = "";
                this.current_user_continuously_learning_days = 0;

                this.current_user_total_learning_days_in_110 = 0;
                this.current_user_learn_detail = "";
                this.current_user_today_learn_detail = "";
                this.current_user_today_review_over_num = "";
                this.current_user_heika_task_text = "";


                this.current_user_review_mes_arr = [];


                this.current_user_overdueReviewArr = [];
                this.current_user_todayReviewArr = [];
                this.current_user_notTodayReviewArr = [];
                this.current_user_finishReviewArr = [];


            }


        },

        computed: {},

        watch: {
            search_user_id: function () {
                //console.log(this.search_user_id);
            },
            today_morning: function (n, o) {

                this.today_night = n + 24 * 3600 - 1;//当天23.59
                this.yesterday_night = n - 1;//昨天晚间23.59
                this.yesterday_morning = this.yesterday_night - 24 * 3600 + 1;//昨天0点
                this.tomorrow_morning = n + 24 * 3600;//明天0点

            }
        }
    });

    /*
     * 开始课程
     * */


    container.ori_time_go();

    //console.log(container.course_audio_time_arr);
    //console.log(container.course_learning_stage_json);

    //console.log(container.is_test_user);


</script>


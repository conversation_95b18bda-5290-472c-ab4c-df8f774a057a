<?php
/*
* Category Template: nce2
*/

if ($_GET['action']) {
    session_start();
    session_unset();
}

pcVisitHandle();


$userData = new userDataTest();//初始化用户对象




$reviewMesHandle = new reviewMesHandle($userData->user_id, $userData->isHeikaVip, current_time("timestamp"));//初始化记忆系统对象


$cat_object = get_the_category();
$catData = new catData($cat_object, 100);//课程页对象
$catData->recordUserViewTime($userData->user_id);//统计用户访问时间


//$userCoursePercent=$catData->getUserCatRatePercent($userData->user_id,$cat_object);//获得该用户本课程的完成度
//$percent_show_text=$userCoursePercent."%";//课程进度文字
//$percent_show_pic=$userCoursePercent*70/100;//课程进度条
//$percent_show_pic.="%";


//myDump($catData->postObjectArr);

$course_rate_of_progress_json = get_user_meta($userData->user_id, "course_rate_of_progress", true);//获取学习进度json

$course_rate_of_progress_arr = array();

if ($course_rate_of_progress_json) {
    $course_rate_of_progress_arr = json_decode($course_rate_of_progress_json);
}


//myDump($course_rate_of_progress_json);

$post_array = array();//文章列表数组

if (count($catData->postObjectArr) > 0) {

    foreach ($catData->postObjectArr as $post_object) {

        $post_status = $reviewMesHandle->get_post_course_status_in_cat($post_object->ID);//课程状态

        /*检查课程进度*/
        $is_have_course_rate_of_progress = 0;

        if (count($course_rate_of_progress_arr) > 0) {


            foreach ($course_rate_of_progress_arr as $progress_arr) {
                if ($post_object->ID == $progress_arr->post_id) {
                    $is_have_course_rate_of_progress = 1;
                    break;
                }
            }

        }


        $arr = array(
            "post_id" => $post_object->ID,
            "course_status" => $post_status[0],
            "finish_times" => $post_status[1],
            "is_unvip_listen" => get_post_meta($post_object->ID, "is_unvip_listen_value", true),
            "post_title" => findNum(get_post($post_object->ID)->post_title),
            "is_have_course_rate_of_progress" => $is_have_course_rate_of_progress

        );

        array_push($post_array, $arr);

    }

}


if (count($post_array) > 0) {
    $post_json = json_encode($post_array);
} else {
    $post_json = "[]";
}


$vipaction_text = get_option("g_heika_vip_slogan");//VIP活动标语
$vipaction_url = get_option("g_vipaction_single_pro_url");//主推商品页


$heika_task_url = home_url() . get_option("g_heika_task_page");//黑卡任务链接



$heika_task_text_arr = $reviewMesHandle->get_heika_tesk_detail();//黑卡任务信息

switch($heika_task_text_arr["task_content"]){

    case "LEARN_TWO":


        $heika_task_content_text = "学习2课";//返回数组

        $learn_num= $heika_task_text_arr["learn_num"];//学习课数

        if($learn_num==0){

            $heika_task_content_completeness = "未完成";//今日任务已完成

        }


        if($learn_num==1){

            $heika_task_content_completeness = "还需学习1课";//今日任务已完成

        }


        if($learn_num>=2){

            $heika_task_content_completeness = "已完成";//今日任务已完成

        }


        break;

    case "LEARN_ONE_REVIEW_ONE":

        $heika_task_content_text = "学习1课、复习1课";//返回数组

        $review_num = $heika_task_text_arr["review_num"];//复习课数

        $learn_num= $heika_task_text_arr["learn_num"];//学习课数



        if ($learn_num >= 1 && $review_num >= 1) {

            $heika_task_content_completeness = "已完成";//今日任务已完成
        } else {


            if ($learn_num >= 1&&$review_num==0) {
                $heika_task_content_completeness = "还需复习1课";//今日任务已完成
            }


            if ($learn_num == 0&&$review_num>=1) {
                $heika_task_content_completeness = "还需学习1课";//今日任务已完成
            }


            if ($learn_num == 0&&$review_num==0) {
                $heika_task_content_completeness = "未完成";//今日任务已完成
            }
        }



        break;

    case "FINISH_ALL_REVIEWING_CONTENT":


        $heika_task_content_text="完成今天所有复习课";

        $last_review_num=$heika_task_text_arr["last_review_num"];//剩余复习数

        if($last_review_num>0){
            $heika_task_content_completeness="还需复习".$last_review_num."课";
        }else{
            $heika_task_content_completeness="已完成";
        }




        break;


    default:
        break;


}




get_header();


?>

<style>

    [v-cloak] {
        display: none;
    }

    .cat_big_wrapper {

        width: 100%;
        float: left;

    }

    .cat_area {
        width: 33%;
        float: left;
        margin-top: 20px;
    }

    .word_cat_area span {
        display: block;
        background: limegreen;
        color: white;
        width: 3em;
        height: 1.5em;
        line-height: 1.5em;
        text-align: center;
        font-size: 0.5em;
        border-radius: 2px;
        margin-top: 10px;

    }

    .cat_lect {
        width: 80px;
        height: 90px;
        color: darkgray;
        border-radius: 5px;
        border: 1px solid #e8e8e8;
        margin: 0px auto;
        box-shadow: 0px 2px 2px #E8E8E8;
        max-width: 80%;

    }

    .cat_number {
        width: 100%;
        height: 26px;
        text-align: center;
        font-size: 1em;
        line-height: 26px;
        color: white;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
    }

    .cat_number_background_grey {
        background: #999999;

    }

    .cat_number_background_red {
        background: #F46966;

    }

    .cat_number_background_blue {
        background: #6CB4E4;

    }

    .cat_number_background_green {
        background: #68D778;

    }

    .cat_number_background_img {
        background: url('/img/newcourse/<EMAIL>');
        background-size: 100% 100%;
        background-repeat: no-repeat;

    }

    .cat_pic {
        width: 100%;
        height: 34px;
    }

    .cat_pic img {
        width: 36%;
        margin-left: 32%;
        margin-top: 8%;

    }

    .cat_status {
        width: 100%;
        height: 30px;
        text-align: center;
        line-height: 30px;
        font-size: 12px;
    }

    .list_area {
        width: 94%;
        float: left;
        margin-left: 3%;
        background: white;
        border-radius: 8px;
        margin-top: 20px;

    }

    .list_area_title {
        width: 100%;
        height: 40px;
        line-height: 40px;
    }

    .list_area_title span:first-child {
        width: 5%;
        float: left;
        display: block;
        height: 20px;
        border-right: 5px solid orangered;
        margin-top: 8px;

    }

    .list_area_title span:nth-child(2) {
        width: 26%;
        display: block;
        height: 36px;
        float: left;
        line-height: 36px;
        text-align: center;
        font-weight: bold;
        font-size: 16px;
    }

    .list_area_title span:nth-child(3) {
        width: 60%;
        display: block;
        height: 36px;
        float: right;
        line-height: 36px;
        text-align: right;
        color: #999;
        font-size: 12px;
    }

    .list_area_title a {
        color: #999;
    }

    .list_area_action {
        width: 100%;
        height: 40px;
        font-size: 14px;
        line-height: 40px;
        text-align: left;
    }

    .list_area_action span {
        color: #999;
        margin-left: 6%;
        font-size: 12px;
    }

    .list_area_action button {
        width: 90%;
        height: 36px;
        margin-top: 12px;
        margin-left: 5%;
        line-height: 36px;
        font-size: 16px;
        border-radius: 10px;
        border: 0;
        background: #FFAA25;
        color: #862116;
    }

    .cat_head {
        width: 100%;
        height: 180px;
        background-color: #F74655;

        color: white;
        /*   z-index: 1;
           top:0px;
           left: 0px;*/
    }

    .cat_head div {
        float: left;
        width: 100%;
        text-align: center;

    }

    .cat_head div:first-child {
        margin-top: 20px;
        font-size: 12px;

    }

    .cat_head div:nth-child(2) {
        margin-top: 10px;

    }

    .cat_head div:nth-child(2) span {

        padding: 4px 10px;
        border: 1px solid white;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
    }

    .warning {
        background: black;
        color: white;
        height: 30px;
        width: 160px;
        opacity: 1;
        z-index: 1000;
        position: absolute;
        line-height: 30px;
        /* padding: 5px; */
        padding: 0px 10px 0px 10px;
        font-size: 12px;
        text-align: center;
        top: 80%;
        border-radius: 5px;
    }

    .bottom_dis {
        width: 100%;
        display: block;
        height: 80px;
        margin: 0px;
        padding: 0px;
        float: left;
    }


</style>


<div id="container" class="cat_big_wrapper">


    <!--头部开始-->
    <div class="cat_head" v-cloak>

        <div>正在学习</div>
        <div>
            <span>{{cat_title}}</span>
        </div>

    </div>
    <!--头部结束-->


    <!--黑卡任务开始-->
    <div class="list_area" style="margin-top: -40px;" v-show="isHeikaVip==1" v-cloak @click.stop="click_heika_task">

        <div class="list_area_title">
            <span></span>

            <span>今日任务</span>


                <span>
                    查看详情&nbsp;>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </span>

        </div>
        <div class="list_area_action">
            <span>{{heika_task_content_text}}</span>
            <span style="margin-left: 1px;">（{{heika_task_content_completeness}}）</span>
        </div>

    </div>
    <!--黑卡任务结束-->


    <!--提示开通黑卡开始-->
    <div class="list_area" style="margin-top: -40px;" v-show="isHeikaVip!=1&&isOldVip!=1" v-cloak>

        <div class="list_area_action">
            <span>{{vipaction_text}}</span>
        </div>

        <div class="list_area_action" style="height: 60px;">
            <a :href="get_vipaction_url()">
                <button>立即开通</button>
            </a>
        </div>

    </div>
    <!--提示开通黑卡结束-->


    <!--课程表开始-->
    <div class="list_area" v-show="isOldVip==1" style="margin-top: -40px;padding-bottom: 30px;" v-cloak>


        <div class="list_area_title">
            <span></span>

            <span>课程表</span>

        </div>


        <div v-for="(post,key) in post_arr">


            <div class="cat_area" @click="click_course(key,post.post_id)">
                <div class="cat_lect">

                    <div class="cat_number cat_number_background_img"
                         v-show="post.is_unvip_listen==1&&post.course_status==0">{{post.post_title}}
                    </div>
                    <div class="cat_number cat_number_background_grey"
                         v-show="post.is_unvip_listen==0&&post.course_status==0">{{post.post_title}}
                    </div>
                    <div class="cat_number cat_number_background_red" v-show="post.course_status==1">
                        {{post.post_title}}
                    </div>
                    <div class="cat_number cat_number_background_blue" v-show="post.course_status==2">
                        {{post.post_title}}
                    </div>
                    <div class="cat_number cat_number_background_green" v-show="post.course_status==3">
                        {{post.post_title}}
                    </div>

                    <div class="cat_pic">
                        <img v-show="post.course_status==0"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">

                        <img v-show="post.course_status==1"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">

                        <img v-show="post.course_status==2&&post.finish_times==1"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">
                        <img v-show="post.course_status==2&&post.finish_times>1"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">


                        <img v-show="post.course_status==3"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">
                    </div>


                    <div v-show="post.course_status==0" class="cat_status">未学习</div>
                    <div v-show="post.course_status==1" class="cat_status" style="color:#F46966">需复习</div>
                    <div v-show="post.course_status==2&&post.finish_times==1" class="cat_status"
                         style="color:#79B8E3">等待复习
                    </div>
                    <div v-show="post.course_status==2&&post.finish_times>1" class="cat_status"
                         style="color:#79B8E3">已复习{{post.finish_times-1}}次
                    </div>
                    <div v-show="post.course_status==3" class="cat_status" style="color:#08aa00">已完成</div>
                </div>
            </div>


        </div>




    </div>
    <!--课程表结束-->


    <!--课程表开始-->
    <div class="list_area" v-show="isOldVip!=1" style="padding-bottom: 30px;"v-cloak>


        <div class="list_area_title">
            <span></span>

            <span>课程表</span>

        </div>


        <div v-for="(post,key) in post_arr">


            <div class="cat_area" @click="click_course(key,post.post_id)">
                <div class="cat_lect">

                    <div class="cat_number cat_number_background_img"
                         v-show="post.is_unvip_listen==1&&post.course_status==0">{{post.post_title}}
                    </div>
                    <div class="cat_number cat_number_background_grey"
                         v-show="post.is_unvip_listen==0&&post.course_status==0">{{post.post_title}}
                    </div>
                    <div class="cat_number cat_number_background_red" v-show="post.course_status==1">
                        {{post.post_title}}
                    </div>
                    <div class="cat_number cat_number_background_blue" v-show="post.course_status==2">
                        {{post.post_title}}
                    </div>
                    <div class="cat_number cat_number_background_green" v-show="post.course_status==3">
                        {{post.post_title}}
                    </div>

                    <div class="cat_pic">
                        <img v-show="post.course_status==0"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">

                        <img v-show="post.course_status==1"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">

                        <img v-show="post.course_status==2&&post.finish_times==1"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">
                        <img v-show="post.course_status==2&&post.finish_times>1"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">


                        <img v-show="post.course_status==3"
                             src="<?php home_url() ?>/img/newcourse/<EMAIL>">
                    </div>


                    <div v-show="post.course_status==0" class="cat_status">未学习</div>
                    <div v-show="post.course_status==1" class="cat_status" style="color:#F46966">需复习</div>
                    <div v-show="post.course_status==2&&post.finish_times==1" class="cat_status"
                         style="color:#79B8E3">等待复习
                    </div>
                    <div v-show="post.course_status==2&&post.finish_times>1" class="cat_status"
                         style="color:#79B8E3">已复习{{post.finish_times-1}}次
                    </div>
                    <div v-show="post.course_status==3" class="cat_status" style="color:#08aa00">已完成</div>
                </div>
            </div>


        </div>


    </div>
    <!--课程表结束-->


    <!--警告提示开始-->
    <div class="warning" v-show="show_warning==true&&warning_text" v-cloak>{{warning_text}}</div>
    <!--警告提示结束-->


    <div class="bottom_dis">

    </div>
</div>


<script>

    var container = new Vue({
        el: '#container',
        data: {

            home_url: "<?php echo home_url()?>",

            post_arr:<?php echo $post_json;?>,
            cat_title: "<?php echo $catData->catName;?>",
            isHeikaVip: "<?php echo $userData->isHeikaVip;?>",
            isOldVip: "<?php echo $userData->isOldVip;?>",
            vipaction_text: "<?php echo $vipaction_text;?>",
            vipaction_url: "<?php echo $vipaction_url;?>",
            heika_task_url: "<?php echo $heika_task_url;?>",
            heika_task_content_text: "<?php echo $heika_task_content_text;?>",
            heika_task_content_completeness: "<?php echo $heika_task_content_completeness;?>",
            warning_text: "",
            show_warning: false


        },


        methods: {

            click_heika_task: function () {
                window.location.href = this.heika_task_url;
            },


            click_course: function (key, post_id) {


                if (key != 0) {
                    /*不是第一节课*/


                    if (this.post_arr[key].is_unvip_listen != 1) {
                        //不可试听


                        if (this.post_arr[key - 1].course_status == 0) {
                            //上节课状态 是0

                            if (this.post_arr[key].course_status == 0) {
                                //这节课状态是0

                                if (this.post_arr[key].is_have_course_rate_of_progress != 1) {
                                    //课程翻页为空

                                    if(this.isHeikaVip==1){
                                        //是黑卡会员
                                        //this.come_warning("请先学完上一课!");
                                        //return;
                                    }

                                }

                            }
                        }
                    }

                }


                window.location.href = this.home_url + "/?p=" + post_id;


            },


            get_vipaction_url: function () {
                return this.vipaction_url;
            },

            get_heika_task_url: function () {

                return this.heika_task_url;
            },

            come_warning: function (text) {

                if (!this.show_warning) {

                    this.warning_text = text;

                    this.show_warning = true;

                    var _this = this;

                    window.setTimeout(function () {
                        _this.show_warning = false;

                    }, 2000)

                }


            }

        },

        computed: {},


        watch: {}
    });


    $("body").css("background", "#F5F5F5");

    /*警告窗口位置设置*/
    var wa = $(".warning");
    var wd = (CLIENTWIDTH - 160) / 2;
    wa.css("left", wd.toString() + "px");


    /*后退强制刷新*/

    var TYPE_NAVIGATE = 0, // 当前页面是通过点击链接，书签和表单提交，或者脚本操作，或者在url中直接输入地址，type值为0
        TYPE_RELOAD = 1, // 点击刷新页面按钮或者通过Location.reload()方法显示的页面，type值为1
        TYPE_BACK_FORWARD = 2, // 页面通过历史记录和前进后退访问时。type值为2
        TYPE_RESERVED = 255; // 任何其他方式，type值为255
    window.addEventListener('pageshow', function (e) {
        if (e.persisted || (window.performance && window.performance.navigation.type == TYPE_BACK_FORWARD)) {
            location.reload()
        }
    }, false)

</script>

<!-- 底部导航组件 - 复刻自index.php -->
<?php
// 获取底部导航配置
$course_text = get_option('g_bottom_nav_course_text');
$course_text = !empty($course_text) ? $course_text : '课程';

$course_url = get_option('g_bottom_nav_course_url');
$course_url = !empty($course_url) ? $course_url : '/';

$me_text = get_option('g_bottom_nav_me_text');
$me_text = !empty($me_text) ? $me_text : '我的';

$me_url = get_option('g_bottom_nav_me_url');
$me_url = !empty($me_url) ? $me_url : '/个人中心/';

// 构建完整URL
$course_full_url = home_url($course_url);
$me_full_url = home_url($me_url);
?>

<style>
/* 自定义底部导航样式 - 完全复刻自index.php */
.custom-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 -4px 20px rgba(247, 70, 85, 0.15);
    z-index: 1000;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-nav-container {
    display: flex;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.custom-nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #666666;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 8px 10px;
    position: relative;
    border-radius: 12px;
    margin: 0 8px;
}

.custom-nav-item:hover {
    text-decoration: none;
    color: #F74655;
    background: rgba(247, 70, 85, 0.05);
    transform: translateY(-2px);
}


.custom-nav-item.active::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    border-radius: 0 0 3px 3px;
}

.custom-nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
}

.custom-nav-item.active .custom-nav-icon {
    transform: scale(1.1);
}

.custom-nav-text {
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
    color: inherit;
    text-align: center;
}

/* 为页面内容添加底部间距，避免被导航遮挡 */
body {
    padding-bottom: 60px !important;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
    .custom-bottom-nav {
        height: 55px;
    }

    body {
        padding-bottom: 55px !important;
    }

    .custom-nav-icon {
        font-size: 18px;
        width: 26px;
        height: 26px;
    }

    .custom-nav-text {
        font-size: 10px;
    }
}

/* 小屏幕设备 */
@media screen and (max-width: 480px) {
    .custom-nav-container {
        margin: 0 5px;
    }

    .custom-nav-item {
        margin: 0 3px;
        padding: 6px 8px;
    }
}
</style>

<div class="custom-bottom-nav">
    <div class="custom-nav-container">
        <!-- 课程按钮标红，表示当前页 -->
        <a href="<?php echo $course_full_url; ?>" class="custom-nav-item">
            <div class="custom-nav-icon">
                <span class="glyphicon glyphicon-education"></span>
            </div>
            <div class="custom-nav-text"><?php echo $course_text; ?></div>
        </a>

        <a href="<?php echo $me_full_url; ?>" class="custom-nav-item">
            <div class="custom-nav-icon">
                <span class="glyphicon glyphicon-user" style="color: #666666;"></span>
            </div>
            <div class="custom-nav-text"><?php echo $me_text; ?></div>
        </a>
    </div>
</div>

<?php

get_sidebar();
get_footer();

?>

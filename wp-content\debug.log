[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] 插件开始加载 - 版本 2.0.0 - 独立运行
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] AJAX处理器开始加载
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_review_get_user_data AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_record_category_visit AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_reset_category_visit AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_review_get_user_review_mes AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] reset_alter_review_time AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] reset_alter_heika_data_time AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] reset_current_user_learn_detail AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] reset_current_user_today_learn_detail AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] reset_current_user_heika_check_in_detail AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content_total AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_review_clear_review_mes AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] get_current_timestamp AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_review_clean_user_all_data AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_reset_user_review_mes_and_course_rate_of_progress AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_review_create_test_review_mes AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] admin_delete_single_course_progress AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] AJAX处理器完整加载完成 - 所有核心函数已迁移
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] AJAX处理器文件已加载
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] 开始测试函数冲突
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_data
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_record_category_visit
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_category_visit
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_review_mes
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_review_time
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_heika_data_time
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_learn_detail
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clear_review_mes
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_get_current_timestamp
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clean_user_all_data
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_data
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_record_category_visit
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_category_visit
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_review_mes
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_review_time
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_heika_data_time
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_learn_detail
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clear_review_mes
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_get_current_timestamp
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clean_user_all_data
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] 重构状态: {"functions_migrated":15,"ajax_actions_preserved":15,"prefix_applied":"lrd_","theme_independence":true}
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] ✅ 未检测到函数冲突
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger-Test] 测试完成，如果没有冲突错误，说明重构成功
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] 测试文件已加载
[10-Jul-2025 07:36:06 UTC] [LearningReviewDebugger] 插件加载完成
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] 插件开始加载 - 版本 1.1.0 - 兼容前端系统
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] AJAX处理器开始加载
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] selectUserVipTime AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] increase_heika_vip_time AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] delete_heika AJAX action已注册
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] AJAX处理器加载完成，所有函数已注册
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] AJAX处理器文件已加载
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] 开始测试函数冲突
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_is_valid_json
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_select_user_vip_time
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_increase_heika_vip_time
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_delete_heika
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_selectUserVipTime
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_increase_heika_vip_time
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_delete_heika
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] VIP卡1 - big_course_type: hlyyin
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] ✅ VIP卡1 兼容前端系统
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager-Test] 测试完成，如果没有冲突错误，说明修复成功
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] 测试文件已加载
[10-Jul-2025 07:36:06 UTC] [VIPTimeManager] 插件加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[10-Jul-2025 07:36:07 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[10-Jul-2025 07:36:07 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[10-Jul-2025 07:36:07 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[10-Jul-2025 07:36:07 UTC] [DomainDebug] upload_url_path: (空值)
[10-Jul-2025 07:36:07 UTC] [DomainDebug] upload_path: (空值)
[10-Jul-2025 07:36:07 UTC] [DomainDebug] ========================================
[10-Jul-2025 07:36:07 UTC] [CustomCategoryTemplates] [初始化] 前台环境，注册模板过滤器
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[10-Jul-2025 07:36:07 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[10-Jul-2025 07:36:07 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[10-Jul-2025 07:36:07 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[10-Jul-2025 07:36:07 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[10-Jul-2025 07:36:07 UTC] [MobileDetector] [步骤 1] 检测User-Agent: mozilla/5.0 (linux; android 14; 23116pn5bc build/ukq1.230804.001; wv) applewebkit/537.36 (khtml, lik...
[10-Jul-2025 07:36:07 UTC] [MobileDetector] [步骤 2] 设备检测结果 - PC:N Mac:N iPhone:N Android:Y iPad:N
[10-Jul-2025 07:36:07 UTC] [MobileDetector] [结果] 检测为Android设备
[10-Jul-2025 07:36:07 UTC] [PCVisitHandler] [步骤 1] 访问检测 - 移动设备:Y 编辑权限:N
[10-Jul-2025 07:36:07 UTC] [PCVisitHandler] [结果] 访问检查通过，允许继续访问
[10-Jul-2025 07:36:07 UTC] [UserInfoQuery] [查询] 用户ID: 361162
[10-Jul-2025 07:36:07 UTC] [UserInfoQuery] [成功] 获取到 33 条用户元数据
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_default_book" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 290
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_post_info" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 295
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_fav_cat" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 298
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "cash_back_times" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 310
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "total_learning_days_cash_back_chance" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 315
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "continuously_learning_days_cash_back_chance" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 319
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_openid_to_weiyouxiin" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 322
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_sign_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 323
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_sign_num" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 324
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_first_log_remind_vip" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 325
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_special_pay" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 326
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "first_pay" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 327
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_share_num" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 330
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_share_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 332
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_drawdailyvip_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 334
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "review_now_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 336
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_drawlearnvip_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 338
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_isknow_youzan" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 344
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_viptime" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 347
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_save_viptime" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 349
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_last_stop_vip_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 351
[10-Jul-2025 07:36:07 UTC] PHP Warning:  Undefined array key "user_orders" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 366
[10-Jul-2025 07:36:07 UTC] PHP Deprecated:  Creation of dynamic property catData::$catView is deprecated in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/catData.class.php on line 71
[10-Jul-2025 07:36:07 UTC] PHP Deprecated:  Creation of dynamic property catData::$catView is deprecated in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/catData.class.php on line 71
[10-Jul-2025 07:36:07 UTC] PHP Deprecated:  Creation of dynamic property catData::$catView is deprecated in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/catData.class.php on line 71
[10-Jul-2025 07:36:07 UTC] PHP Deprecated:  Creation of dynamic property catData::$catView is deprecated in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/catData.class.php on line 71
[10-Jul-2025 07:36:07 UTC] PHP Deprecated:  Creation of dynamic property catData::$catView is deprecated in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/catData.class.php on line 71
[10-Jul-2025 07:36:07 UTC] PHP Deprecated:  Creation of dynamic property catData::$catView is deprecated in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/catData.class.php on line 71
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] 插件开始加载 - 版本 2.0.0 - 独立运行
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] AJAX处理器开始加载
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_review_get_user_data AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_record_category_visit AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_reset_category_visit AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_review_get_user_review_mes AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] reset_alter_review_time AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] reset_alter_heika_data_time AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] reset_current_user_learn_detail AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] reset_current_user_today_learn_detail AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] reset_current_user_heika_check_in_detail AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content_total AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_review_clear_review_mes AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] get_current_timestamp AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_review_clean_user_all_data AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_reset_user_review_mes_and_course_rate_of_progress AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_review_create_test_review_mes AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] admin_delete_single_course_progress AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] AJAX处理器完整加载完成 - 所有核心函数已迁移
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] AJAX处理器文件已加载
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] 开始测试函数冲突
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_data
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_record_category_visit
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_category_visit
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_review_mes
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_review_time
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_heika_data_time
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_learn_detail
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clear_review_mes
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_get_current_timestamp
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clean_user_all_data
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_data
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_record_category_visit
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_category_visit
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_review_mes
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_review_time
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_heika_data_time
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_learn_detail
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clear_review_mes
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_get_current_timestamp
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clean_user_all_data
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] 重构状态: {"functions_migrated":15,"ajax_actions_preserved":15,"prefix_applied":"lrd_","theme_independence":true}
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] ✅ 未检测到函数冲突
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger-Test] 测试完成，如果没有冲突错误，说明重构成功
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] 测试文件已加载
[10-Jul-2025 07:36:08 UTC] [LearningReviewDebugger] 插件加载完成
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] 插件开始加载 - 版本 1.1.0 - 兼容前端系统
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] AJAX处理器开始加载
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] selectUserVipTime AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] increase_heika_vip_time AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] delete_heika AJAX action已注册
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] AJAX处理器加载完成，所有函数已注册
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] AJAX处理器文件已加载
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] 开始测试函数冲突
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_is_valid_json
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_select_user_vip_time
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_increase_heika_vip_time
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_delete_heika
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_selectUserVipTime
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_increase_heika_vip_time
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_delete_heika
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] VIP卡1 - big_course_type: hlyyin
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] ✅ VIP卡1 兼容前端系统
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager-Test] 测试完成，如果没有冲突错误，说明修复成功
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] 测试文件已加载
[10-Jul-2025 07:36:08 UTC] [VIPTimeManager] 插件加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[10-Jul-2025 07:36:08 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[10-Jul-2025 07:36:08 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[10-Jul-2025 07:36:08 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[10-Jul-2025 07:36:08 UTC] [DomainDebug] upload_url_path: (空值)
[10-Jul-2025 07:36:08 UTC] [DomainDebug] upload_path: (空值)
[10-Jul-2025 07:36:08 UTC] [DomainDebug] ========================================
[10-Jul-2025 07:36:08 UTC] [CustomCategoryTemplates] [初始化] 后台环境，注册分类管理钩子
[10-Jul-2025 07:36:08 UTC] [CustomCategoryTemplates] [钩子注册] 分类表单字段和保存操作钩子已注册
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[10-Jul-2025 07:36:08 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[10-Jul-2025 07:36:08 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[10-Jul-2025 07:36:08 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[10-Jul-2025 07:36:08 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[10-Jul-2025 07:36:08 UTC] [CustomPostTemplates] [后台初始化] 支持的文章类型: post
[10-Jul-2025 07:36:08 UTC] [CustomPostTemplates] [元数据框] 已为文章类型 "post" 添加模板选择框
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [开始] 微信JS-SDK签名生成开始
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [步骤 1] 获取access_token: 94_ZsqNU54D_L4rKO0Hd...
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [步骤 2] 构造jsapi_ticket请求URL: https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=94_ZsqNU54D_L4rKO0HdZFzqDuQzlkNh2bUaMqDdC-RUIWl-Xt9-BXEuAzEUb0Yjrv2-1v1GSq9t9m7uoDnQaeLFpz5Dv1ejRnabIr_NMIznq1jrJcBTj4tAB6myrMUFYjAGAALE&type=jsapi
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [步骤 2] jsapi_ticket API响应: {"errcode":0,"errmsg":"ok","ticket":"bxLdikRXVbTPdHSM05e5u5pZYhbAVXBwJWlXMQTOVDhiKPwA7CRSO2HGjBjBEagOSsLEtxN_EKx1Y6qjSD0j5Q","expires_in":7200}
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [步骤 2] jsapi_ticket获取成功: bxLdikRXVbTPdHSM05e5...
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [步骤 3] 签名参数 - timestamp: 1752132967038, nonceStr: weiyouxiin
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [步骤 3] 当前页面URL: https://xgn.shuimitao.online/
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [步骤 4] 待签名字符串: jsapi_ticket=bxLdikRXVbTPdHSM05e5u5pZYhbAVXBwJWlXMQTOVDhiKPwA7CRSO2HGjBjBEagOSsLEtxN_EKx1Y6qjSD0j5Q&noncestr=weiyouxiin&timestamp=1752132967038&url=https://xgn.shuimitao.online/
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [步骤 5] 生成签名: d9511a29769feef27e0d2e47403534ac2567d889
[10-Jul-2025 07:36:08 UTC] [WxJsSDKSign] [完成] 微信JS-SDK签名生成完成
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] 插件开始加载 - 版本 2.0.0 - 独立运行
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] AJAX处理器开始加载
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_get_user_data AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_record_category_visit AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_reset_category_visit AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_get_user_review_mes AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_alter_review_time AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_alter_heika_data_time AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_learn_detail AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_today_learn_detail AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_heika_check_in_detail AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content_total AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_clear_review_mes AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] get_current_timestamp AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_clean_user_all_data AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_reset_user_review_mes_and_course_rate_of_progress AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_create_test_review_mes AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_delete_single_course_progress AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] AJAX处理器完整加载完成 - 所有核心函数已迁移
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] AJAX处理器文件已加载
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] 开始测试函数冲突
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_data
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_record_category_visit
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_category_visit
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_review_mes
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_review_time
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_heika_data_time
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_learn_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clear_review_mes
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_get_current_timestamp
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clean_user_all_data
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_data
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_record_category_visit
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_category_visit
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_review_mes
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_review_time
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_heika_data_time
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_learn_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clear_review_mes
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_get_current_timestamp
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clean_user_all_data
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] 重构状态: {"functions_migrated":15,"ajax_actions_preserved":15,"prefix_applied":"lrd_","theme_independence":true}
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 未检测到函数冲突
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] 测试完成，如果没有冲突错误，说明重构成功
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] 测试文件已加载
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] 插件加载完成
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] 插件开始加载 - 版本 1.1.0 - 兼容前端系统
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] AJAX处理器开始加载
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] selectUserVipTime AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] increase_heika_vip_time AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] delete_heika AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] AJAX处理器加载完成，所有函数已注册
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] AJAX处理器文件已加载
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] 开始测试函数冲突
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_is_valid_json
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_select_user_vip_time
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_increase_heika_vip_time
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_delete_heika
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_selectUserVipTime
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_increase_heika_vip_time
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_delete_heika
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] VIP卡1 - big_course_type: hlyyin
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ VIP卡1 兼容前端系统
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] 测试完成，如果没有冲突错误，说明修复成功
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] 测试文件已加载
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] 插件加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[10-Jul-2025 07:36:10 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[10-Jul-2025 07:36:10 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[10-Jul-2025 07:36:10 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[10-Jul-2025 07:36:10 UTC] [DomainDebug] upload_url_path: (空值)
[10-Jul-2025 07:36:10 UTC] [DomainDebug] upload_path: (空值)
[10-Jul-2025 07:36:10 UTC] [DomainDebug] ========================================
[10-Jul-2025 07:36:10 UTC] [CustomCategoryTemplates] [初始化] 前台环境，注册模板过滤器
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[10-Jul-2025 07:36:10 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[10-Jul-2025 07:36:10 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[10-Jul-2025 07:36:10 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[10-Jul-2025 07:36:10 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[10-Jul-2025 07:36:10 UTC] [MobileDetector] [步骤 1] 检测User-Agent: mozilla/5.0 (linux; android 14; 23116pn5bc build/ukq1.230804.001; wv) applewebkit/537.36 (khtml, lik...
[10-Jul-2025 07:36:10 UTC] [MobileDetector] [步骤 2] 设备检测结果 - PC:N Mac:N iPhone:N Android:Y iPad:N
[10-Jul-2025 07:36:10 UTC] [MobileDetector] [结果] 检测为Android设备
[10-Jul-2025 07:36:10 UTC] [PCVisitHandler] [步骤 1] 访问检测 - 移动设备:Y 编辑权限:N
[10-Jul-2025 07:36:10 UTC] [PCVisitHandler] [结果] 访问检查通过，允许继续访问
[10-Jul-2025 07:36:10 UTC] [UserInfoQuery] [查询] 用户ID: 361162
[10-Jul-2025 07:36:10 UTC] [UserInfoQuery] [成功] 获取到 33 条用户元数据
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_default_book" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 290
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_post_info" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 295
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_fav_cat" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 298
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "cash_back_times" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 310
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "total_learning_days_cash_back_chance" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 315
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "continuously_learning_days_cash_back_chance" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 319
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_openid_to_weiyouxiin" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 322
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_sign_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 323
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_sign_num" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 324
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_first_log_remind_vip" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 325
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_special_pay" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 326
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "first_pay" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 327
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_share_num" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 330
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_share_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 332
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_drawdailyvip_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 334
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "review_now_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 336
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_drawlearnvip_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 338
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_isknow_youzan" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 344
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_viptime" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 347
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_save_viptime" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 349
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_last_stop_vip_time" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 351
[10-Jul-2025 07:36:10 UTC] PHP Warning:  Undefined array key "user_orders" in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/userTotalData.class.php on line 366
[10-Jul-2025 07:36:10 UTC] PHP Fatal error:  Uncaught TypeError: count(): Argument #1 ($value) must be of type Countable|array, stdClass given in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/reviewMesHandle.class.php:520
Stack trace:
#0 /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/reviewMesHandle.class.php(86): reviewMesHandle->heika_data_auto_update()
#1 /www/wwwroot/shuimitao.online/wp-content/themes/html5game/page-me.php(33): reviewMesHandle->__construct()
#2 /www/wwwroot/shuimitao.online/wp-includes/template-loader.php(106): include('...')
#3 /www/wwwroot/shuimitao.online/wp-blog-header.php(19): require_once('...')
#4 /www/wwwroot/shuimitao.online/index.php(17): require('...')
#5 {main}
  thrown in /www/wwwroot/shuimitao.online/wp-content/themes/html5game/class/reviewMesHandle.class.php on line 520
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] 插件开始加载 - 版本 2.0.0 - 独立运行
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] AJAX处理器开始加载
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_get_user_data AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_record_category_visit AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_reset_category_visit AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_get_user_review_mes AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_alter_review_time AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_alter_heika_data_time AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_learn_detail AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_today_learn_detail AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_heika_check_in_detail AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content_total AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_clear_review_mes AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] get_current_timestamp AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_clean_user_all_data AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_reset_user_review_mes_and_course_rate_of_progress AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_review_create_test_review_mes AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] admin_delete_single_course_progress AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] AJAX处理器完整加载完成 - 所有核心函数已迁移
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] AJAX处理器文件已加载
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] 开始测试函数冲突
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_data
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_record_category_visit
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_category_visit
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_review_mes
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_review_time
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_heika_data_time
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_learn_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clear_review_mes
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_get_current_timestamp
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clean_user_all_data
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_data
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_record_category_visit
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_category_visit
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_review_mes
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_review_time
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_heika_data_time
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_learn_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clear_review_mes
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_get_current_timestamp
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clean_user_all_data
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] 重构状态: {"functions_migrated":15,"ajax_actions_preserved":15,"prefix_applied":"lrd_","theme_independence":true}
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] ✅ 未检测到函数冲突
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger-Test] 测试完成，如果没有冲突错误，说明重构成功
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] 测试文件已加载
[10-Jul-2025 07:36:10 UTC] [LearningReviewDebugger] 插件加载完成
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] 插件开始加载 - 版本 1.1.0 - 兼容前端系统
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] AJAX处理器开始加载
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] selectUserVipTime AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] increase_heika_vip_time AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] delete_heika AJAX action已注册
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] AJAX处理器加载完成，所有函数已注册
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] AJAX处理器文件已加载
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] 开始测试函数冲突
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_is_valid_json
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_select_user_vip_time
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_increase_heika_vip_time
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_delete_heika
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_selectUserVipTime
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_increase_heika_vip_time
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_delete_heika
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] VIP卡1 - big_course_type: hlyyin
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] ✅ VIP卡1 兼容前端系统
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager-Test] 测试完成，如果没有冲突错误，说明修复成功
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] 测试文件已加载
[10-Jul-2025 07:36:10 UTC] [VIPTimeManager] 插件加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[10-Jul-2025 07:36:11 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[10-Jul-2025 07:36:11 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[10-Jul-2025 07:36:11 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[10-Jul-2025 07:36:11 UTC] [DomainDebug] upload_url_path: (空值)
[10-Jul-2025 07:36:11 UTC] [DomainDebug] upload_path: (空值)
[10-Jul-2025 07:36:11 UTC] [DomainDebug] ========================================
[10-Jul-2025 07:36:11 UTC] [CustomCategoryTemplates] [初始化] 前台环境，注册模板过滤器
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[10-Jul-2025 07:36:11 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[10-Jul-2025 07:36:11 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[10-Jul-2025 07:36:11 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] 插件开始加载 - 版本 2.0.0 - 独立运行
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] AJAX处理器开始加载
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_review_get_user_data AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_record_category_visit AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_reset_category_visit AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_review_get_user_review_mes AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] reset_alter_review_time AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] reset_alter_heika_data_time AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] reset_current_user_learn_detail AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] reset_current_user_today_learn_detail AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] reset_current_user_heika_check_in_detail AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] reset_current_user_heika_daily_learning_task_content_total AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_review_clear_review_mes AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] get_current_timestamp AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_review_clean_user_all_data AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_reset_user_review_mes_and_course_rate_of_progress AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_review_create_test_review_mes AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] admin_delete_single_course_progress AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] AJAX处理器完整加载完成 - 所有核心函数已迁移
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] AJAX处理器文件已加载
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] 开始测试函数冲突
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_data
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_record_category_visit
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_category_visit
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_get_user_review_mes
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_review_time
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_alter_heika_data_time
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_learn_detail
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clear_review_mes
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_get_current_timestamp
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_review_clean_user_all_data
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 函数存在: lrd_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_data
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_record_category_visit
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_category_visit
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_get_user_review_mes
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_review_time
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_alter_heika_data_time
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_learn_detail
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_today_learn_detail
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_check_in_detail
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_reset_current_user_heika_daily_learning_task_content_total
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clear_review_mes
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_get_current_timestamp
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_review_clean_user_all_data
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ AJAX action已注册: wp_ajax_admin_reset_user_review_mes_and_course_rate_of_progress
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] 重构状态: {"functions_migrated":15,"ajax_actions_preserved":15,"prefix_applied":"lrd_","theme_independence":true}
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] ✅ 未检测到函数冲突
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger-Test] 测试完成，如果没有冲突错误，说明重构成功
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] 测试文件已加载
[10-Jul-2025 07:36:11 UTC] [LearningReviewDebugger] 插件加载完成
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] 插件开始加载 - 版本 1.1.0 - 兼容前端系统
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] AJAX处理器开始加载
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] selectUserVipTime AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] increase_heika_vip_time AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] delete_heika AJAX action已注册
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] AJAX处理器加载完成，所有函数已注册
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] AJAX处理器文件已加载
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] 开始测试函数冲突
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_is_valid_json
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_select_user_vip_time
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_increase_heika_vip_time
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] ✅ 函数存在: vip_manager_delete_heika
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_selectUserVipTime
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_increase_heika_vip_time
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] ✅ AJAX action已注册: wp_ajax_delete_heika
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] VIP卡1 - big_course_type: hlyyin
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] ✅ VIP卡1 兼容前端系统
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager-Test] 测试完成，如果没有冲突错误，说明修复成功
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] 测试文件已加载
[10-Jul-2025 07:36:11 UTC] [VIPTimeManager] 插件加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [初始化] 开始加载HTML5Game主题functions.php
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [配置] 有赞商城URL配置完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] 开始加载核心类文件
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] wechat.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] courseData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] postData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] userData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] userDataTest.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] userTotalData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] reviewMesHandle.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] reviewMesHandleTest.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] heikaHandle.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [类加载] catData.class.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] 开始加载主题扩展功能
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] theme_options.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] simple-term-meta.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] post_field_fun.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] cat_field_fun.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [扩展加载] ajax.php 加载完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [API加载] AipSpeech.php (百度语音API) 文件缺失，已跳过加载
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [定时任务] 定时任务模块已启用，微信access_token将每10分钟自动刷新
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [初始化] 所有核心文件加载完成，开始定义工具函数
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [WordPress设置] 已隐藏前端管理工具栏
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [WordPress钩子] 访问计数功能已注册到wp_head
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [WordPress过滤器] Cookie过期时间自定义功能已注册
[10-Jul-2025 07:36:11 UTC] [DomainDebug] ========== WordPress域名选项检查 ==========
[10-Jul-2025 07:36:11 UTC] [DomainDebug] siteurl: https://xgn.shuimitao.online
[10-Jul-2025 07:36:11 UTC] [DomainDebug] home: https://xgn.shuimitao.online
[10-Jul-2025 07:36:11 UTC] [DomainDebug] upload_url_path: (空值)
[10-Jul-2025 07:36:11 UTC] [DomainDebug] upload_path: (空值)
[10-Jul-2025 07:36:11 UTC] [DomainDebug] ========================================
[10-Jul-2025 07:36:11 UTC] [CustomCategoryTemplates] [初始化] 前台环境，注册模板过滤器
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [功能模块] 自定义分类模板功能已启用 (整合自Custom Category Templates插件)
[10-Jul-2025 07:36:11 UTC] [CustomPostTemplates] [初始化] 开始注册自定义文章模板钩子
[10-Jul-2025 07:36:11 UTC] [CustomPostTemplates] [钩子注册] 文章模板相关钩子已注册完成
[10-Jul-2025 07:36:11 UTC] [ThemeFunctions] [功能模块] 自定义文章模板功能已启用 (整合自Custom Post Templates插件)
[10-Jul-2025 07:36:11 UTC] 微信支付订单插件 - [证书工具] 警告：平台证书保存路径设置成功，但保存到数据库选项失败或未改变 (值未改变)：/www/wwwroot/shuimitao.online/wp-content/uploads/wechat-certs/wechatpay_platform_certs.pem

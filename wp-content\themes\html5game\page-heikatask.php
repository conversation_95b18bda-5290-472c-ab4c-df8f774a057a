<?php
/*
Template Name:heikatask
*/


if ($_GET['action']) {
    session_start();
    session_unset();
}

pcVisitHandle();

$userData = new userDataTest();//用户对象


$reviewMesHandle = new reviewMesHandle($userData->user_id, current_time("timestamp"), $userData->isHeikaVip);//记忆系统对象

$heikaHandle = new heikaHandle($userData->user_id);//黑卡对象


//$userData->isHeikaVip=0;


$vipaction_text = get_option("g_vipaction_text");//VIP活动标语
$vipaction_url = get_option("g_vipaction_single_pro_url");//主推商品页

$ajax_url = admin_url("admin-ajax.php");//ajax地址

$nowTime = current_time("timestamp");//现在时间
$today_morning = strtotime(current_time("Y-m-d"));//今天早上





$continuously_learning_days = $reviewMesHandle->get_heika_continuously_learning_days();//连续学习天数


$total_learning_days_in_110=$reviewMesHandle->get_heika_total_learning_days_in_110();//110天内总共学习天数







$heika_tesk_obj = $reviewMesHandle->get_heika_tesk_detail();//黑卡任务数组

// === 调试代码开始 ===
error_log('[HEIKA_DEBUG] =====开始调试今日任务显示问题=====');
error_log('[HEIKA_DEBUG] heika_tesk_obj: ' . print_r($heika_tesk_obj, true));
if (empty($heika_tesk_obj)) {
    error_log('[HEIKA_ERROR] heika_tesk_obj is empty!');
}
// === 调试代码结束 ===

$task_content = $heika_tesk_obj['task_content'];//今日任务内容
$learn_num = ($heika_tesk_obj['learn_num']) ? $heika_tesk_obj['learn_num'] : 0;//学课数
$review_num = ($heika_tesk_obj['review_num']) ? $heika_tesk_obj['review_num'] : 0;//复习数
$last_review_num = ($heika_tesk_obj["last_review_num"]) ? $heika_tesk_obj["last_review_num"] : 0;//剩余复习数

// === 调试代码开始 ===
error_log('[HEIKA_DEBUG] 任务参数解析结果:');
error_log('[HEIKA_DEBUG] - task_content: ' . $task_content);
error_log('[HEIKA_DEBUG] - learn_num: ' . $learn_num);
error_log('[HEIKA_DEBUG] - review_num: ' . $review_num);
error_log('[HEIKA_DEBUG] - last_review_num: ' . $last_review_num);
// === 调试代码结束 ===

/*
 *
 * 今天任务是否已完成
 * */


$is_finish_heika_daily_learning_task = 0;//1或0


$last_sign_obj = end($reviewMesHandle->heika_check_in_detail_arr);//上次签到时间是最后一个元素

$is_finish_heika_daily_learning_task = $last_sign_obj->is_finish_heika_daily_learning_task;//今日是否完成任务

// === 调试代码开始 ===
error_log('[HEIKA_DEBUG] 签到数据分析:');
error_log('[HEIKA_DEBUG] - heika_check_in_detail_arr count: ' . count($reviewMesHandle->heika_check_in_detail_arr));

if (empty($reviewMesHandle->heika_check_in_detail_arr)) {
    error_log('[HEIKA_ERROR] heika_check_in_detail_arr is empty!');
} else {
    error_log('[HEIKA_DEBUG] - last_sign_obj type: ' . gettype($last_sign_obj));
    if (is_object($last_sign_obj)) {
        if (property_exists($last_sign_obj, 'is_finish_heika_daily_learning_task')) {
            error_log('[HEIKA_DEBUG] - original is_finish_heika_daily_learning_task: ' . $last_sign_obj->is_finish_heika_daily_learning_task);
        } else {
            error_log('[HEIKA_ERROR] Property is_finish_heika_daily_learning_task not found!');
        }
    } else {
        error_log('[HEIKA_ERROR] last_sign_obj is not an object!');
    }
}

error_log('[HEIKA_DEBUG] - Final is_finish_heika_daily_learning_task: ' . $is_finish_heika_daily_learning_task);
error_log('[HEIKA_DEBUG] =====调试信息记录完成=====');
// === 调试代码结束 ===

$today_review_url = home_url() . get_option("g_today_review_page");//今日复习url



$webUrl = home_url();//网站域名




get_header();


if ($userData->isLogin) {

    ?>


    <style>
        [v-cloak] {
            display: none;
        }

        .goVip {
            width: 100%;
            position: fixed;
            top: 20%;
            left: 0px;
        }

        .goVip div {
            width: 100%;
            float: left;
            text-align: center;
            color: gray;
            font-size: 14px;

        }

        .goVip img {
            width: 40%;
            display: block;
            margin-left: 30%;
            float: left;

        }

        .goVip button {
            width: 50%;
            float: left;
            display: block;
            border: 0px;
            background-color: limegreen;
            color: white;
            font-size: 16px;
            margin-top: 40px;
            margin-left: 25%;
            height: 40px;
            border-radius: 10px;
            text-align: center;
            line-height: 40px;
        }

        .task_mes {
            width: 100%;
            float: left;
            background-color: #F74655;
            color: white;
        }

        .task_mes > div{
            width: 100%;
            float: left;

        }







        .task_mes_days{
            height: 100px;
            font-size: 14px;
            margin-top: 10px;

        }

        .task_mes_days > div{
            width: 50%;
            float: left;
            height: 100px;
        }

        .task_mes_days > div > span:first-child{
            display: block;
            height: 60px;
            font-size: 32px;
            line-height: 60px;
            text-align: center;
            font-weight: bold;
        }


        .task_mes_days > div > span:nth-child(2){
            height: 40px;
            text-align: center;
            display: block;
            line-height: 10px;
            font-size: 12px;
        }

        .task_mes_buttons{
            height: 80px;
        }

        .task_mes_buttons > div{
            width: 100%;
            float: left;
        }

        .task_mes_buttons > div:first-child{
            height: 50px;

        }


        .task_mes_buttons > div:first-child > button{
            width: 40%;
            margin-left: 30%;
            height: 40px;
            margin-top: 5px;
            border-radius: 10px;
            border: 0;
        }

        .task_mes_buttons > div:nth-child(2){
            height: 30px;

        }




        .list_area_title {
            width: 100%;
            height: 50px;
            line-height: 50px;
            font-size: 14px;


        }

        .list_area_title span:first-child {
            margin-left: 10px;

        }

        .list_area_title span:nth-child(2) {
            margin-left: 5px;

        }

        .list_area_title span:nth-child(3) {
            float: right;
            font-size: 12px;
            color: #999;
            margin-right: 5%;

        }

        .list_area_action {
            width: 100%;
            font-size: 14px;
            line-height: 30px;
            text-align: left;
        }

        .list_area_action span {
            color: #999;
            margin-left: 6%;
            font-size: 12px;
        }

        .list_area_action button {
            width: 90%;
            height: 36px;
            margin-top: 12px;
            margin-left: 5%;
            line-height: 36px;
            font-size: 16px;
            border-radius: 10px;
            border: 0;
            background: #FFAA25;
            color: #862116;
        }


        .list_area_action p{
            width: 94%;
            font-size: 12px;
            float: left;
            color: darkgray;
            margin-left: 3%;
            text-align: justify;
            word-break: break-all;
        }

        .list_area_calendar {
            width: 100%;
            height: 80px;
            font-size: 14px;
            line-height: 80px;
            margin-top: 5px;
        }

        .list_area_calendar > div:first-child {
            float: left;
            width: 8%;
            height: 80px;
            line-height: 80px;
            text-align: center;
            font-size: 22px;

        }

        .list_area_calendar > div:nth-child(2) {
            float: left;
            width: 83%;
            height: 80px;

        }

        .list_area_calendar > div:nth-child(2) > div {
            width: 14%;
            height: 80px;
            float: left;

        }

        .list_area_calendar > div:nth-child(2) > div > div {
            width: 100%;
            height: 40px;
            float: left;
            line-height: 40px;
            text-align: center;

        }

        .list_area_calendar > div:nth-child(2) > div > div > span {
            width: 70%;
            height: 30px;
            border-radius: 5px;
            float: left;
            line-height: 30px;
            text-align: center;
            margin-top: 2px;
            margin-left: 15%;

            font-size: 12px;

        }

        .list_area_calendar > div:nth-child(3) {
            float: right;
            width: 8%;
            height: 80px;
            line-height: 80px;
            text-align: center;
            font-size: 22px;

        }

        .list_area_color {
            width: 100%;
            height: 50px;
            line-height: 50px;
            text-align: center;
            color: #999;
            font-size: 12px;

        }

        .list_area_full {
            width: 100%;
            float: left;

            background: white;
            margin-top: 20px;

        }

        .list_area_task_title {
            width: 100%;
            height: 40px;
            line-height: 40px;
            float: left;
        }

        .list_area_task_title > div {
            width: 26%;
            display: block;
            height: 36px;
            float: left;
            line-height: 36px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }

        .list_area_task_content {
            width: 100%;
            margin-top: 10px;
            float: left;
        }

        .list_area_task_lect {
            width: 90%;
            float: left;
            height: 100px;
            margin-left: 5%;
            font-size: 14px;
        }

        .list_area_task_lect > div {
            float: left;
            height: 100px;
        }

        .list_area_task_lect > div:first-child {
            width: 50%;

        }

        .list_area_task_lect > div:first-child > div:first-child {
            width: 100%;
            height: 26px;
            font-size: 14px;
            line-height: 26px;

        }

        .list_area_task_lect > div:first-child > div:nth-child(2) {
            font-size: 12px;
            color: #999;
            height: 24px;
            text-align: left;
        }

        .list_area_task_lect > div:nth-child(2) {
            width: 49%;
            height: 100px;
            float: right;
            font-size: 12px;
            /* text-align: center; */
            text-align: right;
            line-height: 50px;

        }

        .bottom_dis {
            width: 100%;
            display: block;
            height: 200px;
            margin: 0px;
            padding: 0px;
            float: left;
        }

        .list_area {
            width: 94%;
            float: left;
            margin-left: 3%;
            background: white;
            border-radius: 8px;
            margin-top: 20px;

        }


    </style>

    <div id="container">

        <!--提示升级黑卡-->
        <div class="goVip" v-show="isHeikaVip==0" v-cloak>
            <div>
                <img src="/img/goVip.png"/>
            </div>
            <div>该功能需要开通黑卡VIP</div>
            <div style="margin-top: 10px" v-show="!is_old_system_vip_over_100()">{{vipaction_text}}</div>

            <div style="margin-top: 10px" v-show="is_old_system_vip_over_100()">您的剩余VIP时间充足，可免费升级黑卡VIP</div>
            <div>

                <button v-show="is_old_system_vip_over_100()" @click.stop="old_viptime_go_to_heika">免费升级黑卡VIP</button>

                <a v-show="!is_old_system_vip_over_100()" :href="get_vipaction_url()">
                    <button>开通黑卡VIP</button>
                </a>
            </div>
        </div>
        <!--提示升级黑卡-->

        <template v-if="isHeikaVip==1" v-cloak>


            <!--连续学习天数-->
            <div class="task_mes">





                <div class="task_mes_days">
                    <div class="continuously_learning">
                        <span>
                           {{continuously_learning_days}}
                        </span>
                        <span>连续学习天数</span>

                    </div>


                    <div class="total_learning">
                        <span>{{total_learning_days_in_110}}</span>
                        <span>累积学习天数</span>

                    </div>

                </div>






            </div>
            <!--连续学习天数-->


            <!--日历-->
            <div class="list_area_full" v-cloak>


                <!--标题-->
                <div class="list_area_title">
                    <span class="glyphicon glyphicon-calendar" aria-hidden="true" style="color: #F74655"></span>
                    <span>学习日历</span>
                    <span>{{this_Monday_year}}年{{this_Monday_month}}月</span>
                </div>
                <!--标题-->


                <div class="list_area_action" style="height: 30px;line-height: 30px">
                    <p>Tips：完成学习任务 增加一天“学习天数” </p>
                </div>


                <div class="list_area_calendar">


                    <div @click.stop="back_a_week">
                        <span class="glyphicon glyphicon-menu-left" aria-hidden="true"></span>
                    </div>


                    <div>

                        <template v-for="obj in dates" v-show="dates.length>0">


                            <div>
                                <div>{{get_day_in_chinese(obj.day)}}</div>
                                <div>
                                          <span style="background-color:#F4C345;color: white"
                                                v-show="!is_today(obj)&&is_today_check_in(obj)">{{obj.date}}</span>
                                    <span v-show="!is_today(obj)&&!is_today_check_in(obj)">{{obj.date}}</span>

                                          <span style='border: 1px solid #F4C345;color: #F4C345;'
                                                v-show="is_today(obj)&&!is_today_check_in(obj)">今</span>
                                          <span style="background-color:#F4C345;color: white"
                                                v-show="is_today(obj)&&is_today_check_in(obj)">今</span>
                                </div>
                            </div>


                        </template>
                    </div>


                    <div v-if="!is_dates_equal_current_week()" @click.stop="forward_a_week">
                        <span class="glyphicon glyphicon-menu-right" aria-hidden="true"></span>
                    </div>

                </div>


                <div class="list_area_color">
                    <span style="color:#F4C345">■</span>
                    <span>已完成</span>
                </div>

            </div>
            <!--日历-->


            <!--今日任务-->
            <div class="list_area_full">

                <!--标题-->
                <div class="list_area_title">
                    <span class="glyphicon glyphicon-file" aria-hidden="true" style="color: #F74655"></span>
                    <span>今日剩余任务</span>
                </div>
                <!--标题-->

                <!--已完成-->
                <div class="list_area_task_content" v-show="is_finish_heika_daily_learning_task==1">
                    <div class="weui-loadmore weui-loadmore_line">
                        <span class="weui-loadmore__tips" style="font-size: 12px">任务已全部完成</span>
                    </div>
                </div>
                <!--已完成-->


                <!--学两课-->
                <div class="list_area_task_content"
                     v-show="task_content=='LEARN_TWO'&&is_finish_heika_daily_learning_task==0">

                    <div class="list_area_task_lect" @click.stop="go_to_web_url">
                        <div>
                            <div>
                                <span>学习任意2课</span>
                                <span></span>
                            </div>
                            <div v-if="learn_num==0">剩余2课</div>
                            <div v-if="learn_num==1">剩余1课</div>


                        </div>

                        <div>
                            <div style="color: #999;">
                                去学习&nbsp;>>
                            </div>
                        </div>
                    </div>
                </div>
                <!--学两课-->


                <!--学一课，复一课-->
                <div class="list_area_task_content"
                     v-show="task_content=='LEARN_ONE_REVIEW_ONE'&&is_finish_heika_daily_learning_task==0">

                    <div class="list_area_task_lect" @click.stop="go_to_web_url" v-show="learn_num==0">
                        <div>
                            <div>
                                <span>学习任意1课</span>
                                <span></span>
                            </div>

                            <div>剩余1课</div>
                        </div>

                        <div>
                            <div style="color: #999;">
                                去学习&nbsp;>>
                            </div>
                        </div>
                    </div>


                    <div class="list_area_task_lect" @click.stop="go_to_today_review_url" v-show="review_num==0">
                        <div>
                            <div>
                                <span>复习任意1课</span>
                                <span></span>
                            </div>
                            <div>剩余1课</div>
                        </div>

                        <div>
                            <div style="color: #999;">
                                去复习&nbsp;>>
                            </div>
                        </div>
                    </div>
                </div>
                <!--学一课，复一课-->


                <!--完成所有复习-->
                <div class="list_area_task_content"
                     v-show="task_content=='FINISH_ALL_REVIEWING_CONTENT'&&is_finish_heika_daily_learning_task==0">

                    <div class="list_area_task_lect" @click.stop="go_to_today_review_url">
                        <div>
                            <div>
                                <span>完成所有复习课程</span>
                                <span></span>
                            </div>

                            <div>剩余{{last_review_num}}课</div>

                        </div>

                        <div>

                            <div style="color: #999;">
                                去复习&nbsp;>>
                            </div>
                        </div>
                    </div>

                </div>
                <!--完成所有复习-->


            </div>
            <!--今日任务-->

            <div class="bottom_dis">

            </div>


        </template>


    </div>


    <script>

<?php
// === 调试代码开始 ===
error_log('[HEIKA_DEBUG] Data passed to frontend:');
error_log('[HEIKA_DEBUG] - task_content: ' . $task_content);
error_log('[HEIKA_DEBUG] - learn_num: ' . $learn_num);
error_log('[HEIKA_DEBUG] - review_num: ' . $review_num);
error_log('[HEIKA_DEBUG] - last_review_num: ' . $last_review_num);
error_log('[HEIKA_DEBUG] - is_finish_heika_daily_learning_task: ' . $is_finish_heika_daily_learning_task);
// === 调试代码结束 ===
?>

        var container = new Vue({
            el: '#container',
            data: {

                user_id: "<?php echo $userData->user_id;?>",//用户id
                user_avatar: "<?php echo $userData->user_avatar;?>",
                user_nickname: "<?php echo $userData->user_nickname;?>",

                web_url: "<?php echo home_url();?>",


                today_review_url: "<?php echo $today_review_url;?>",

                vipaction_text: "<?php echo $vipaction_text;?>",//vip广告语
                vipaction_url: "<?php echo $vipaction_url;?>",//店铺链接
                isOldVip: "<?php echo $userData->isOldVip;?>",//是否老会员VIP
                isHeikaVip: "<?php echo $userData->isHeikaVip;?>",//是否黑卡VIP
                old_system_last_time:<?php echo $heikaHandle->old_system_last_time;?>,//老系统遗留时间

                AJAX_ERROR_NUM: 0,//ajax错误数
                ajax_url: "<?php echo $ajax_url;?>",//ajaxurl

                continuously_learning_days:<?php echo $continuously_learning_days;?>,//连续学习天数
                total_learning_days_in_110:<?php echo $total_learning_days_in_110;?>,//连续学习天数







                nowTime:<?php echo $nowTime;?>,//现在时间戳
                today_morning:<?php echo $today_morning;?>,//今天早上时间戳


                today_morning_millisecond_in_js: 0,//js时间戳，毫秒 ,今天早上


                this_Monday_morning_millisecond_in_js: 0,//本周第一天早上毫秒数
                this_Monday_month: 0,//周一所在月份数
                this_Monday_year: 0,//周一所在年份数


                heika_check_in_detail:<?php echo $reviewMesHandle->heika_check_in_detail;?>,//黑卡check in 详情


                task_content: "<?php echo $task_content;?>",//日常任务内容
                learn_num: <?php echo $learn_num;?>,//学课数
                review_num: <?php echo $review_num;?>,//复习数
                last_review_num:<?php echo $last_review_num;?>,//剩余复习数
                is_finish_heika_daily_learning_task:<?php echo $is_finish_heika_daily_learning_task;?>,

                dates: []//日历渲染数组


            },


            methods: {

                auto_Monday_set: function () {


                    this.today_morning_millisecond_in_js = this.today_morning * 1000 - (8 * 3600 * 1000);//今天早上毫秒数

                    var d = new Date();//日期对象

                    d.setTime(this.today_morning_millisecond_in_js);//设置今天时间
                    var d_day = d.getDay();//今天星期几


                    if (d_day == 0) {
                        d_day = 7;
                    }

                    console.log("今日系统内日期:" + d_day);


                    var this_Monday_morning_millisecond_in_js = this.today_morning_millisecond_in_js - (d_day - 1) * 24 * 3600 * 1000;//本周一早上毫秒数

                    this.come_week_dates(this_Monday_morning_millisecond_in_js);


                },


                is_today: function (obj) {

                    if (obj.morning_millisecond_in_js == this.today_morning_millisecond_in_js) {
                        return true;
                    }

                    return false;
                },


                is_today_check_in: function (obj) {

                    /*
                     *
                     * 今天是否完成了任务
                     * */


                    var check_in_timestamps = [];//完成日常任务的日期时间戳

                    /*
                     * 在 heika_check_in_detail 找寻 归纳为 check_in_timestamps
                     * */

                    for (var i = 0; i < this.heika_check_in_detail.length; i++) {

                        if (this.heika_check_in_detail[i].is_finish_heika_daily_learning_task == 1) {
                            console.log(this.heika_check_in_detail[i].day_timestamp);
                            check_in_timestamps.push(this.heika_check_in_detail[i].day_timestamp);
                        }


                    }


                    /*
                     * check_in_timestamps 有时间戳 处于 obj.morning_timestamp_in_system 和 obj.night_timestamp_in_system 之间
                     * 说明今天签到成功
                     * */


                    for (var j = 0; j < check_in_timestamps.length; j++) {
                        if (check_in_timestamps[j] >= obj.morning_timestamp_in_system && check_in_timestamps[j] <= obj.night_timestamp_in_system) {
                            return true;

                        }
                    }


                    return false;
                },


                is_dates_equal_current_week: function () {

                    var d = new Date();//日期对象

                    d.setTime(this.today_morning_millisecond_in_js);//设置今天时间
                    var d_day = d.getDay();//今天星期几

                    if (d_day == 0) {
                        d_day = 7;
                    }


                    var this_Monday_morning_millisecond_in_js = this.today_morning_millisecond_in_js - (d_day - 1) * 24 * 3600 * 1000;


                    if (this_Monday_morning_millisecond_in_js == this.this_Monday_morning_millisecond_in_js) {
                        return true;
                    }


                    return false;

                },


                come_week_dates: function (this_Monday_morning_millisecond_in_js) {

                    this.this_Monday_morning_millisecond_in_js = this_Monday_morning_millisecond_in_js;


                    for (var i = 0; i < 7; i++) {


                        var d3 = new Date();
                        var morning_millisecond_in_js = this.this_Monday_morning_millisecond_in_js + i * 24 * 3600 * 1000;

                        d3.setTime(morning_millisecond_in_js);//设置本周一时间


                        var r = {};


                        r.day = d3.getDay();
                        r.month = d3.getMonth() + 1;
                        r.year = d3.getFullYear();

                        if (i == 0) {
                            this.this_Monday_month = r.month;//本周1属于第几月
                            this.this_Monday_year = r.year;//本周1属于什么年份
                        }

                        r.morning_timestamp_in_system = morning_millisecond_in_js / 1000 + 8 * 3600;
                        r.night_timestamp_in_system = morning_millisecond_in_js / 1000 + 8 * 3600 + 24 * 3600 - 1;
                        r.morning_millisecond_in_js = morning_millisecond_in_js;
                        r.date = d3.getDate();

                        this.dates.push(r);
                    }

                },

                back_a_week: function () {
                    //alert("向后一周");

                    this.this_Monday_morning_millisecond_in_js -= 7 * 24 * 3600 * 1000;


                },
                forward_a_week: function () {


                    this.this_Monday_morning_millisecond_in_js += 7 * 24 * 3600 * 1000;
                },


                get_day_in_chinese: function (day) {

                    var chinese_day;
                    switch (day) {
                        case 1:
                            chinese_day = "一";
                            break;
                        case 2:
                            chinese_day = "二";
                            break;
                        case 3:
                            chinese_day = "三";
                            break;
                        case 4:
                            chinese_day = "四";
                            break;
                        case 5:
                            chinese_day = "五";
                            break;
                        case 6:
                            chinese_day = "六";
                            break;
                        case 0:
                            chinese_day = "日";
                            break;

                    }
                    return chinese_day;

                },




                go_to_today_review_url: function () {
                    window.location = this.today_review_url;
                },

                go_to_web_url: function () {
                    window.location = this.web_url;
                },


                get_vipaction_url: function () {
                    return this.vipaction_url;
                },


                is_old_system_vip_over_100: function () {

                    if (this.old_system_last_time >= 100 * 24 * 3600) {
                        return true;
                    }

                    return false;

                },


                old_viptime_go_to_heika: function () {
                    //alert("老会员升级为黑卡");

                    var _this = this;

                    $$alert("提示", "升级为黑卡VIP之后，会员有效期不会产生变化，连续一段时间完成学习任务，还可获得现金奖励。", function () {

                        $$loading("加载中");

                        _this.old_viptime_go_to_heika_ajax({
                            user_id: _this.user_id,
                            action: 'upgrade_old_to_heika'
                        }, function (res) {

                            $$closeLoading();


                            if (res != 0) {

                                $$alert("提示", "升级成功", function () {

                                    location.reload()

                                })

                            } else {
                                $$alert("提示", "升级失败，请重试！", function () {
                                    location.reload()

                                })
                            }
                        });

                    });


                },


                old_viptime_go_to_heika_ajax: function (data, callback) {
                    var _this = this;


                    var doAjax = function () {
                        //ajax函数
                        $.ajax({
                            url: _this.ajax_url,
                            type: 'post',
                            data: data,
                            success: function (data, status) {
                                _this.AJAX_ERROR_NUM = 0;//ajax错误状态归0


                                //将结果转为字符串，去掉收尾空格
                                var res = data.toString().trim();
                                callback(res);

                            },
                            error: function () {
                                _this.AJAX_ERROR_NUM += 1;//ajax错误状态递增
                                if (_this.AJAX_ERROR_NUM >= 4) {
                                    _this.AJAX_ERROR_NUM = 0;//ajax错误状态归0
                                } else {
                                    doAjax();//第二次以上执行
                                }
                            }


                        });

                    };

                    doAjax();//执行AJAX

                }


            },

            computed: {},


            watch: {
                this_Monday_morning_millisecond_in_js: function (n, o) {

                    this.dates = [];

                    this.come_week_dates(n);

                    console.log(this.dates);

                }

            }
        });


        container.auto_Monday_set();


        $("body").css("background", "#F5F5F5");


        /*后退强制刷新*/

        var TYPE_NAVIGATE = 0, // 当前页面是通过点击链接，书签和表单提交，或者脚本操作，或者在url中直接输入地址，type值为0
            TYPE_RELOAD = 1, // 点击刷新页面按钮或者通过Location.reload()方法显示的页面，type值为1
            TYPE_BACK_FORWARD = 2, // 页面通过历史记录和前进后退访问时。type值为2
            TYPE_RESERVED = 255; // 任何其他方式，type值为255
        window.addEventListener('pageshow', function (e) {
            if (e.persisted || (window.performance && window.performance.navigation.type == TYPE_BACK_FORWARD)) {
                location.reload()
            }
        }, false)


    </script>


    <?php
} else {
    get_login_page();//提示登录
}

get_footer();

?>
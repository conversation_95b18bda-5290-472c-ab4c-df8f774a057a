---
alwaysApply: false
---



# WordPress黑卡学习记录系统详解

## **系统概述**
黑卡学习记录系统是WordPress主题中的核心功能，负责记录用户学习行为、管理复习计划、处理每日打卡任务。系统仅对黑卡会员开放完整功能。

## **核心文件结构**

### **主要类文件**
- **[reviewMesHandle.class.php](mdc:wp-content/themes/html5game/class/reviewMesHandle.class.php)** - 核心处理类
- **[page-reviewAjax.php](mdc:wp-content/themes/html5game/page-reviewAjax.php)** - AJAX接口处理
- **[single.php](mdc:wp-content/themes/html5game/single.php)** - 课程页面（旧版）
- **[single-20210604.php](mdc:wp-content/themes/html5game/single-20210604.php)** - 课程页面（新版）

### **数据库字段**
系统使用WordPress的`user_meta`表存储用户数据：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `learn_detail` | JSON | 用户总体学习行为记录 |
| `today_learn_detail` | JSON | 今日学习行为记录 |
| `heika_check_in_detail` | JSON | 黑卡打卡详情记录 |
| `review_mes` | JSON | 复习计划数据 |
| `heika_daily_learning_task_content` | String | 每日任务类型 |
| `alter_heika_data_time` | Timestamp | 黑卡数据刷新时间 |

## **核心类：reviewMesHandle**

### **类属性详解**

#### **用户基础信息**
```php
public $user_id;                    // 用户ID
public $isHeikaVip = 0;            // 黑卡会员状态（0=普通用户，1=黑卡会员）
public $now_time;                  // 当前时间戳
public $today_morning;             // 今日凌晨0点时间戳
public $today_night;               // 今日23:59:59时间戳
public $yesterday_night;           // 昨日23:59:59时间戳
public $yesterday_morning;         // 昨日凌晨0点时间戳
public $tomorrow_morning;          // 明日凌晨0点时间戳
```

#### **学习行为记录**
```php
public $learn_detail;              // 总体学习行为JSON字符串
public $learn_detail_arr = array(); // 总体学习行为数组
public $today_learn_detail;        // 今日学习行为JSON字符串
public $today_learn_detail_arr = array(); // 今日学习行为数组
```

#### **复习计划管理**
```php
public $review_mes_json = "";      // 复习计划JSON字符串
public $review_mes_arr = array();  // 复习计划数组
public $today_need_review_num = 0; // 今日需复习课程数量
public $todayReviewArr = array();  // 今日复习课程数组
public $notTodayReviewArr = array(); // 非今日复习课程数组
public $finishReviewArr = array(); // 已完成复习课程数组
```

#### **黑卡任务系统**
```php
public $heika_daily_learning_task_content; // 黑卡每日任务内容
public $is_finish_heika_daily_learning_task = false; // 是否完成黑卡任务
public $heika_check_in_detail;     // 签到详情JSON字符串
public $heika_check_in_detail_arr = array(); // 签到详情数组
public $alter_heika_data_time;     // 黑卡数据刷新时间
public $return_cash_action = 1;    // 返现活动开关
```

### **构造函数**
```php
public function __construct($user_id, $isHeikaVip, $nowTime = "")
```

**参数说明：**
- `$user_id` - 用户ID
- `$isHeikaVip` - 黑卡会员状态（0或1）
- `$nowTime` - 当前时间戳（可选，默认使用当前时间）

**初始化流程：**
1. 设置用户ID和时间相关变量
2. 调用 `review_mes_auto_update()` 更新复习计划
3. 调用 `heika_data_auto_update()` 更新黑卡数据

## **核心方法详解**

### **学习行为记录：save_user_learn_detail()**
```php
public function save_user_learn_detail($post_id, $course_status)
```

**功能：** 记录用户学习行为（仅黑卡会员）

**参数：**
- `$post_id` - 课程ID
- `$course_status` - 课程状态（0=新学习，1=复习）

**处理逻辑：**
1. 获取现有学习记录：`learn_detail` 和 `today_learn_detail`
2. **关键判断：** `if ($this->isHeikaVip == 1)` - 仅黑卡会员记录
3. 添加新记录到两个数组：
   ```php
   array_push($learn_detail_arr, array(
       "post_id" => $post_id,
       "course_status" => $course_status,
       "timestamp" => $this->today_morning + 3
   ));
   ```
4. 保存到数据库：`update_user_meta()`

**返回值：**
- `true` - 黑卡会员且保存成功
- `false` - 非黑卡会员或保存失败

### **复习计划保存：save_user_review_mes()**
```php
public function save_user_review_mes($post_id, $cat_id, $word_str = "", $sentence_str = "")
```

**功能：** 保存用户复习计划（所有用户）

**参数：**
- `$post_id` - 课程ID
- `$cat_id` - 课程分类ID
- `$word_str` - 单词字符串（可选）
- `$sentence_str` - 句子字符串（可选）

**复习计划数据结构：**
```php
array(
    "post_id" => $post_id,           // 课程ID
    "finishTimes" => 1,              // 完成次数
    "model" => $cat_id,              // 课程分类ID
    "reviewTime" => $next_review_time // 下次复习时间
)
```

### **黑卡打卡处理：set_heika_check_in_detail()**
```php
public function set_heika_check_in_detail($return_arr)
```

**功能：** 处理黑卡打卡逻辑和任务完成状态

**打卡成功条件：**
1. **基础条件：** `$this->isHeikaVip == 1` （必须是黑卡会员）
2. **任务完成：** 根据任务类型判断是否完成

**每日任务类型：**

#### **LEARN_TWO - 学习两课**
```php
if ($learn_num >= 2) {
    $is_finish_heika_daily_learning_task = 1;
}
```

#### **LEARN_ONE_REVIEW_ONE - 学一课复习一课**
```php
if ($learn_num >= 1 && $review_num >= 1) {
    $is_finish_heika_daily_learning_task = 1;
}
```

#### **FINISH_ALL_REVIEWING_CONTENT - 完成所有复习内容**
```php
if ($last_today_review == 0) {
    $is_finish_heika_daily_learning_task = 1;
}
```

**打卡记录更新：**
```php
$last_sign_obj->is_finish_heika_daily_learning_task = 1;
update_user_meta($this->user_id, "heika_check_in_detail", json_encode($heika_check_in_detail_arr));
```

## **AJAX接口：page-reviewAjax.php**

### **接口参数**
```php
$user_id = $_POST['user_id'];           // 用户ID
$isHeikaVip = $_POST['isHeikaVip'];     // 黑卡会员状态
$course_status = $_POST['course_status']; // 课程状态
$nowTime = $_POST['nowTime'];           // 当前时间戳
$post_id = $_POST['post_id'];           // 课程ID
$model = $_POST['model'];               // 课程分类ID
```

### **处理流程**
1. **参数验证：** 检查必需参数是否存在
2. **对象初始化：** `new reviewMesHandle($user_id, $isHeikaVip, $nowTime)`
3. **保存复习计划：** 调用 `save_user_review_mes()`
4. **保存学习行为：** 调用 `save_user_learn_detail()`（仅黑卡会员）
5. **处理打卡：** 调用 `set_heika_check_in_detail()`
6. **返回结果：** JSON格式响应

### **返回数据结构**
```php
array(
    'save_user_review_mes' => 1,                    // 复习计划保存状态
    'save_user_learn_detail' => 1,                  // 学习行为保存状态
    'heika_daily_learning_task_content' => 'LEARN_TWO', // 任务类型
    'is_finish_heika_daily_learning_task' => 1,     // 任务完成状态
    'learn_num' => 2,                               // 学习课数
    'review_num' => 1,                              // 复习课数
    'last_today_review' => 0,                       // 剩余复习数
    'is_inform_user' => 1                           // 是否通知用户
)
```

## **前端调用方式**

### **single.php 调用**
```javascript
this.save_review_mes_ajax({
    user_id: _this.user_id,
    isHeikaVip: _this.isHeikaVip,
    course_status: _this.course_status,
    nowTime: _this.nowTime,
    action: 1,
    post_id: _this.post_id,
    model: _this.post_cat_id
}, function (res) {
    // 处理响应
});
```

### **AJAX URL配置**
- **配置选项：** `g_course_review_ajax_url`
- **指向页面：** 使用 `reviewAjax` 模板的页面
- **获取方式：** `get_option("g_course_review_ajax_url")`

## **数据流转图**

```
用户完成课程
    ↓
前端调用 save_user_review_mes()
    ↓
AJAX请求到 page-reviewAjax.php
    ↓
初始化 reviewMesHandle 对象
    ↓
保存复习计划 (所有用户)
    ↓
保存学习行为 (仅黑卡会员)
    ↓
处理打卡逻辑 (仅黑卡会员)
    ↓
返回JSON响应
    ↓
前端更新UI状态
```

## **重要注意事项**

### **黑卡会员限制**
- **学习行为记录：** 仅黑卡会员记录 `learn_detail` 和 `today_learn_detail`
- **打卡功能：** 仅黑卡会员可以打卡和完成每日任务
- **复习计划：** 所有用户都可以保存复习计划

### **时间处理**
- **标准时间：** `$this->today_morning + 20` 作为当日标准时间
- **记录时间：** `$this->today_morning + 3` 作为学习记录时间戳
- **时区处理：** 使用 `current_time("timestamp")` 获取WordPress时区时间

### **数据一致性**
- **每日刷新：** 系统会在用户首次登录时清空 `today_learn_detail`
- **连续打卡：** 通过 `heika_check_in_detail` 计算连续学习天数
- **任务重置：** 每日任务在凌晨自动重置

### **错误处理**
- **参数验证：** 检查必需参数是否存在
- **权限检查：** 验证黑卡会员状态
- **数据库操作：** 使用 `update_user_meta()` 的返回值判断成功状态

## **调试建议**

### **常见问题排查**
1. **学习行为未记录：** 检查用户是否为黑卡会员 (`isHeikaVip == 1`)
2. **AJAX调用失败：** 检查 `g_course_review_ajax_url` 配置
3. **打卡未成功：** 检查每日任务完成条件
4. **数据不一致：** 检查时间戳计算和数据库更新

### **调试工具**
- **LearningReviewDebugger插件：** 查看用户学习数据
- **WordPress调试日志：** 查看错误信息
- **浏览器开发者工具：** 检查AJAX请求和响应

## **系统扩展**

### **新增任务类型**
在 `set_heika_check_in_detail()` 方法的 `switch` 语句中添加新的 `case`：

```php
case "NEW_TASK_TYPE":
    if ($custom_condition) {
        $is_finish_heika_daily_learning_task = 1;
    }
    break;
```

### **数据统计**
- **连续打卡天数：** `get_heika_continuously_learning_days()`
- **总学习天数：** `get_heika_total_learning_days_in_110()`
- **学习进度：** 通过 `learn_detail_arr` 统计

---

**文档版本：** 1.0  
**最后更新：** 2025-07-10  
**维护者：** WordPress开发团队


# WordPress黑卡学习记录系统详解

## **系统概述**
黑卡学习记录系统是WordPress主题中的核心功能，负责记录用户学习行为、管理复习计划、处理每日打卡任务。系统仅对黑卡会员开放完整功能。

## **核心文件结构**

### **主要类文件**
- **[reviewMesHandle.class.php](mdc:wp-content/themes/html5game/class/reviewMesHandle.class.php)** - 核心处理类
- **[page-reviewAjax.php](mdc:wp-content/themes/html5game/page-reviewAjax.php)** - AJAX接口处理
- **[single.php](mdc:wp-content/themes/html5game/single.php)** - 课程页面（旧版）
- **[single-20210604.php](mdc:wp-content/themes/html5game/single-20210604.php)** - 课程页面（新版）

### **数据库字段**
系统使用WordPress的`user_meta`表存储用户数据：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `learn_detail` | JSON | 用户总体学习行为记录 |
| `today_learn_detail` | JSON | 今日学习行为记录 |
| `heika_check_in_detail` | JSON | 黑卡打卡详情记录 |
| `review_mes` | JSON | 复习计划数据 |
| `heika_daily_learning_task_content` | String | 每日任务类型 |
| `alter_heika_data_time` | Timestamp | 黑卡数据刷新时间 |

## **核心类：reviewMesHandle**

### **类属性详解**

#### **用户基础信息**
```php
public $user_id;                    // 用户ID
public $isHeikaVip = 0;            // 黑卡会员状态（0=普通用户，1=黑卡会员）
public $now_time;                  // 当前时间戳
public $today_morning;             // 今日凌晨0点时间戳
public $today_night;               // 今日23:59:59时间戳
public $yesterday_night;           // 昨日23:59:59时间戳
public $yesterday_morning;         // 昨日凌晨0点时间戳
public $tomorrow_morning;          // 明日凌晨0点时间戳
```

#### **学习行为记录**
```php
public $learn_detail;              // 总体学习行为JSON字符串
public $learn_detail_arr = array(); // 总体学习行为数组
public $today_learn_detail;        // 今日学习行为JSON字符串
public $today_learn_detail_arr = array(); // 今日学习行为数组
```

#### **复习计划管理**
```php
public $review_mes_json = "";      // 复习计划JSON字符串
public $review_mes_arr = array();  // 复习计划数组
public $today_need_review_num = 0; // 今日需复习课程数量
public $todayReviewArr = array();  // 今日复习课程数组
public $notTodayReviewArr = array(); // 非今日复习课程数组
public $finishReviewArr = array(); // 已完成复习课程数组
```

#### **黑卡任务系统**
```php
public $heika_daily_learning_task_content; // 黑卡每日任务内容
public $is_finish_heika_daily_learning_task = false; // 是否完成黑卡任务
public $heika_check_in_detail;     // 签到详情JSON字符串
public $heika_check_in_detail_arr = array(); // 签到详情数组
public $alter_heika_data_time;     // 黑卡数据刷新时间
public $return_cash_action = 1;    // 返现活动开关
```

### **构造函数**
```php
public function __construct($user_id, $isHeikaVip, $nowTime = "")
```

**参数说明：**
- `$user_id` - 用户ID
- `$isHeikaVip` - 黑卡会员状态（0或1）
- `$nowTime` - 当前时间戳（可选，默认使用当前时间）

**初始化流程：**
1. 设置用户ID和时间相关变量
2. 调用 `review_mes_auto_update()` 更新复习计划
3. 调用 `heika_data_auto_update()` 更新黑卡数据

## **核心方法详解**

### **学习行为记录：save_user_learn_detail()**
```php
public function save_user_learn_detail($post_id, $course_status)
```

**功能：** 记录用户学习行为（仅黑卡会员）

**参数：**
- `$post_id` - 课程ID
- `$course_status` - 课程状态（0=新学习，1=复习）

**处理逻辑：**
1. 获取现有学习记录：`learn_detail` 和 `today_learn_detail`
2. **关键判断：** `if ($this->isHeikaVip == 1)` - 仅黑卡会员记录
3. 添加新记录到两个数组：
   ```php
   array_push($learn_detail_arr, array(
       "post_id" => $post_id,
       "course_status" => $course_status,
       "timestamp" => $this->today_morning + 3
   ));
   ```
4. 保存到数据库：`update_user_meta()`

**返回值：**
- `true` - 黑卡会员且保存成功
- `false` - 非黑卡会员或保存失败

### **复习计划保存：save_user_review_mes()**
```php
public function save_user_review_mes($post_id, $cat_id, $word_str = "", $sentence_str = "")
```

**功能：** 保存用户复习计划（所有用户）

**参数：**
- `$post_id` - 课程ID
- `$cat_id` - 课程分类ID
- `$word_str` - 单词字符串（可选）
- `$sentence_str` - 句子字符串（可选）

**复习计划数据结构：**
```php
array(
    "post_id" => $post_id,           // 课程ID
    "finishTimes" => 1,              // 完成次数
    "model" => $cat_id,              // 课程分类ID
    "reviewTime" => $next_review_time // 下次复习时间
)
```

### **黑卡打卡处理：set_heika_check_in_detail()**
```php
public function set_heika_check_in_detail($return_arr)
```

**功能：** 处理黑卡打卡逻辑和任务完成状态

**打卡成功条件：**
1. **基础条件：** `$this->isHeikaVip == 1` （必须是黑卡会员）
2. **任务完成：** 根据任务类型判断是否完成

**每日任务类型：**

#### **LEARN_TWO - 学习两课**
```php
if ($learn_num >= 2) {
    $is_finish_heika_daily_learning_task = 1;
}
```

#### **LEARN_ONE_REVIEW_ONE - 学一课复习一课**
```php
if ($learn_num >= 1 && $review_num >= 1) {
    $is_finish_heika_daily_learning_task = 1;
}
```

#### **FINISH_ALL_REVIEWING_CONTENT - 完成所有复习内容**
```php
if ($last_today_review == 0) {
    $is_finish_heika_daily_learning_task = 1;
}
```

**打卡记录更新：**
```php
$last_sign_obj->is_finish_heika_daily_learning_task = 1;
update_user_meta($this->user_id, "heika_check_in_detail", json_encode($heika_check_in_detail_arr));
```

## **AJAX接口：page-reviewAjax.php**

### **接口参数**
```php
$user_id = $_POST['user_id'];           // 用户ID
$isHeikaVip = $_POST['isHeikaVip'];     // 黑卡会员状态
$course_status = $_POST['course_status']; // 课程状态
$nowTime = $_POST['nowTime'];           // 当前时间戳
$post_id = $_POST['post_id'];           // 课程ID
$model = $_POST['model'];               // 课程分类ID
```

### **处理流程**
1. **参数验证：** 检查必需参数是否存在
2. **对象初始化：** `new reviewMesHandle($user_id, $isHeikaVip, $nowTime)`
3. **保存复习计划：** 调用 `save_user_review_mes()`
4. **保存学习行为：** 调用 `save_user_learn_detail()`（仅黑卡会员）
5. **处理打卡：** 调用 `set_heika_check_in_detail()`
6. **返回结果：** JSON格式响应

### **返回数据结构**
```php
array(
    'save_user_review_mes' => 1,                    // 复习计划保存状态
    'save_user_learn_detail' => 1,                  // 学习行为保存状态
    'heika_daily_learning_task_content' => 'LEARN_TWO', // 任务类型
    'is_finish_heika_daily_learning_task' => 1,     // 任务完成状态
    'learn_num' => 2,                               // 学习课数
    'review_num' => 1,                              // 复习课数
    'last_today_review' => 0,                       // 剩余复习数
    'is_inform_user' => 1                           // 是否通知用户
)
```

## **前端调用方式**

### **single.php 调用**
```javascript
this.save_review_mes_ajax({
    user_id: _this.user_id,
    isHeikaVip: _this.isHeikaVip,
    course_status: _this.course_status,
    nowTime: _this.nowTime,
    action: 1,
    post_id: _this.post_id,
    model: _this.post_cat_id
}, function (res) {
    // 处理响应
});
```

### **AJAX URL配置**
- **配置选项：** `g_course_review_ajax_url`
- **指向页面：** 使用 `reviewAjax` 模板的页面
- **获取方式：** `get_option("g_course_review_ajax_url")`

## **数据流转图**

```
用户完成课程
    ↓
前端调用 save_user_review_mes()
    ↓
AJAX请求到 page-reviewAjax.php
    ↓
初始化 reviewMesHandle 对象
    ↓
保存复习计划 (所有用户)
    ↓
保存学习行为 (仅黑卡会员)
    ↓
处理打卡逻辑 (仅黑卡会员)
    ↓
返回JSON响应
    ↓
前端更新UI状态
```

## **重要注意事项**

### **黑卡会员限制**
- **学习行为记录：** 仅黑卡会员记录 `learn_detail` 和 `today_learn_detail`
- **打卡功能：** 仅黑卡会员可以打卡和完成每日任务
- **复习计划：** 所有用户都可以保存复习计划

### **时间处理**
- **标准时间：** `$this->today_morning + 20` 作为当日标准时间
- **记录时间：** `$this->today_morning + 3` 作为学习记录时间戳
- **时区处理：** 使用 `current_time("timestamp")` 获取WordPress时区时间

### **数据一致性**
- **每日刷新：** 系统会在用户首次登录时清空 `today_learn_detail`
- **连续打卡：** 通过 `heika_check_in_detail` 计算连续学习天数
- **任务重置：** 每日任务在凌晨自动重置

### **错误处理**
- **参数验证：** 检查必需参数是否存在
- **权限检查：** 验证黑卡会员状态
- **数据库操作：** 使用 `update_user_meta()` 的返回值判断成功状态

## **调试建议**

### **常见问题排查**
1. **学习行为未记录：** 检查用户是否为黑卡会员 (`isHeikaVip == 1`)
2. **AJAX调用失败：** 检查 `g_course_review_ajax_url` 配置
3. **打卡未成功：** 检查每日任务完成条件
4. **数据不一致：** 检查时间戳计算和数据库更新

### **调试工具**
- **LearningReviewDebugger插件：** 查看用户学习数据
- **WordPress调试日志：** 查看错误信息
- **浏览器开发者工具：** 检查AJAX请求和响应

## **系统扩展**

### **新增任务类型**
在 `set_heika_check_in_detail()` 方法的 `switch` 语句中添加新的 `case`：

```php
case "NEW_TASK_TYPE":
    if ($custom_condition) {
        $is_finish_heika_daily_learning_task = 1;
    }
    break;
```

### **数据统计**
- **连续打卡天数：** `get_heika_continuously_learning_days()`
- **总学习天数：** `get_heika_total_learning_days_in_110()`
- **学习进度：** 通过 `learn_detail_arr` 统计

---

**文档版本：** 1.0  
**最后更新：** 2025-07-10  
**维护者：** WordPress开发团队

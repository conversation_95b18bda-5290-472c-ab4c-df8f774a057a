<?php
/*
Template Name:setting
*/

if ($_GET['action']) {
    session_start();
    session_unset();
}

pcVisitHandle();


$userData = new userDataTest();//初始化用户对象


$reviewMesHandle = new reviewMesHandle($userData->user_id, $userData->isHeikaVip, current_time("timestamp"));//初始化记忆系统对象


get_header();

if ($userData->isLogin) {

    $is_reject_reiview_mes = get_user_meta($userData->user_id, "is_reject_reiview_mes", true);

    if(!$is_reject_reiview_mes){
        $is_reject_reiview_mes=0;
    }



    $is_have_review_mes = (count($reviewMesHandle->review_mes_arr) > 0) ? 1 : 0;

    // myDump($is_reject_reiview_mes);


    ?>

    <style>
        .weui-cell:before {
            border: 0px;
        }

        .weui-cell {
            margin-top: 20px
        }
    </style>

    <div id="container">


        <div class="weui-cell weui-cell_switch">
            <div class="weui-cell__bd">课程进度与复习计划</div>
            <div v-show="is_have_review_mes==1" class="weui-cell__ft" style="color: red" @click.stop="clean_user_all_review_mes">
                清空所有内容
            </div>

            <div v-show="is_have_review_mes==0" class="weui-cell__ft" style="color: #e8e8e8">
                清空所有内容
            </div>
        </div>


        <div class="weui-cell weui-cell_switch">
            <div class="weui-cell__bd">开启/关闭复习提醒</div>
            <div class="weui-cell__ft">
                <input class="weui-switch" type="checkbox" name="review_mes_switch" v-if="is_reject_reiview_mes==0"
                       checked="checked" @click.stop="set_user_reject_review_mes">
                <input class="weui-switch" type="checkbox" name="review_mes_switch" v-if="is_reject_reiview_mes==1"
                       @click.stop="set_user_reject_review_mes">
            </div>
        </div>

    </div>


    <script>

        var container = new Vue({
            el: '#container',
            data: {


                is_reject_reiview_mes:<?php echo $is_reject_reiview_mes;?>,
                is_have_review_mes:<?php echo $is_have_review_mes;?>


            },


            methods: {

                clean_user_all_review_mes: function () {
                    $$confirm("提示", "清空所有进度和复习计划之后，今日学习任务将被重置（需重新学习），是否继续？", function () {

                        $$HTTPPOST("<?php echo admin_url( 'admin-ajax.php' );?>", {
                            user_id: <?php echo $userData->user_id;?>,
                            action: 'clean_user_all_review_mes'
                        }, function (e) {

                            console.log(e);

                            if(e!=0){
                                $$alert("提示","清理成功!",function(){
                                    location.reload()

                                })
                            }else{
                                $$alert("提示","清理失败!请重试！",function(){
                                    location.reload()

                                })
                            }


                        }, true);

                    })
                },

                set_user_reject_review_mes: function () {
                    var _this = this;

                    if (this.is_reject_reiview_mes == 1) {
                        this.is_reject_reiview_mes = 0;
                    } else {
                        this.is_reject_reiview_mes = 1
                    }

                    console.log(this.is_reject_reiview_mes);


                    $$HTTPPOST("<?php echo admin_url( 'admin-ajax.php' );?>", {
                        user_id: <?php echo $userData->user_id;?>,
                        action: 'set_user_reject_review_mes',
                        k: _this.is_reject_reiview_mes
                    }, function (e) {

                        console.log(e);


                    }, true);


                }
            },

            computed: {},


            watch: {}
        });


    </script>

    <?php

} else {
    get_login_page();//提示登录
}


get_footer();

?>
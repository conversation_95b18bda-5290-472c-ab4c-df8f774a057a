# 网站运维关键信息梳理

## 一、系统基础信息

### 1.1 操作系统
- **系统版本**：CentOS Linux release 7.8.2003 (Core)

### 1.2 CPU信息
- **架构**：x86_64
- **CPU op-mode**：32-bit, 64-bit
- **字节序**：Little Endian
- **CPU数量**：2
- **每核线程数**：1
- **每插槽核心数**：2
- **插槽数**：1
- **NUMA节点数**：1
- **厂商ID**：GenuineIntel
- **CPU家族**：6
- **型号**：94
- **型号名称**：Intel(R) Xeon(R) Gold 6133 CPU @ 2.50GHz
- **步进**：3
- **CPU MHz**：2494.130
- **BogoMIPS**：4988.26
- **虚拟化厂商**：KVM
- **虚拟化类型**：full
- **缓存**：
  - L1d：32K
  - L1i：32K
  - L2：4096K
  - L3：28160K
- **NUMA节点0 CPU**：0,1
- **标志**：fpu vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss ht syscall nx pdpe1gb rdtscp lm constant_tsc rep_good nopl eagerfpu pni pclmulqdq ssse3 fma cx16 pcid sse4_1 sse4_2 x2apic movbe popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm abm 3dnowprefetch invpcid_single rsb_ctxsw fsgsbase bmi1 hle avx2 smep bmi2 erms invpcid rtm mpx rdseed adx smap clflushopt xsaveopt xsavec xgetbv1 arat

### 1.3 内存信息
- **总物理内存 (Total Mem)**：约 3.6 GB
- **当前可用物理内存 (Available Mem, 截至 2025-05-04 约 08:00)**：约 1.4 GB
- **总 Swap 空间 (Total Swap)**：3.0 GB (包括 `/www/swap` 1GB 和 `/swapfile_new` 2GB)
- **当前已用 Swap (Used Swap, 截至 2025-05-04 约 08:00)**：约 1.1 GB

### 1.4 系统负载
- **运行时间**：(根据实际情况更新)
- **当前用户**：(根据实际情况更新)
- **负载情况**：(根据实际情况更新)

### 1.5 磁盘使用
- **根目录**：总空间:59GB, 已用:(根据实际情况更新)GB, 可用:(根据实际情况更新)GB, 使用率:(根据实际情况更新)%

## 二、应用软件环境

### 2.1 核心应用
- **WordPress**：6.7.1
- **Nginx**：1.25.5
- **PHP**：8.2.27
- **MySQL**：5.7.44-log

### 2.2 面板管理工具
- **宝塔Linux面板**：9.3.0
  - **面板端口**：8888
  - **面板账号**：`hv2dczjw`
  - **面板密码**：`MS_Showlin123`
  - **面板密钥**：`db2dc1941103`

## 三、网络与安全配置

### 3.1 网络信息
- **公网IP**：***************
- **内网IP**：*********

### 3.2 防火墙配置
- **状态**：启用 (Active Running)
- **开放端口**：
  - FTP：20(数据传输), 21(控制连接), 39000-40000(被动模式)
  - SSH：22
  - Web：80(HTTP), 443(HTTPS), 8888(宝塔面板), 8089(Dify HTTP 映射), 8443(Dify HTTPS 映射)
  - 数据库：3306(MySQL)
  - 自定义：30000-35000
- **允许服务**：dhcpv6-client, ssh
- **日志记录**：已启用 (`--set-log-denied=all`)
- **安全设置**：`AllowZoneDrifting` 已禁用，规则更安全

### 3.3 安全防护
- **DDoS防护**：专业版，100Gbps

## 四、数据库配置

### 4.1 MySQL基本信息
- **版本**：MySQL 5.7.44-log
- **服务路径**：`/www/server/mysql/bin/mysqld`
- **数据目录**：`/www/server/data`
- **权限设置**：`drwxr-x---`，仅 `mysql:mysql` 用户组可访问

### 4.2 数据库账户
- **root用户**：
  - **密码**：`b376aea9d61e1286`
- **业务数据库**：
  - **名称**：`shuimitao_online`
  - **用户**：`shuimitao_online`
  - **密码**：`n2MBWNSkGW`
  - **主机**：`localhost`
- **Dify 数据库 (PostgreSQL, 运行于 Docker)**:
  - **数据库名**: `dify` (来自 `.env` 文件 `DB_DATABASE`)
  - **用户名**: `postgres` (来自 `.env` 文件 `DB_USERNAME`)
  - **密码**: `difyai123456` (来自 `.env` 文件 `DB_PASSWORD`)
  - **访问方式**: 仅限 Docker 内部网络访问 (主机 `db`, 端口 `5432`)

### 4.3 安全设置
- **SSL/TLS**：已启用
- **加密套件**：`AES128-SHA256:AES256-SHA256`

## 五、Web服务配置

### 5.1 Nginx基本信息
- **版本**：1.25.5 (宝塔主 Nginx)
- **配置文件**：`/www/server/nginx/conf/nginx.conf`
- **监听端口**：80(HTTP), 443(HTTPS), 8888(宝塔), 888(自定义)

### 5.2 日志配置
- **错误日志**：
  - `/www/wwwlogs/nginx_error.log`
  - `/www/wwwlogs/tcp-error.log`
- **访问日志**：
  - `/www/wwwlogs/access.log`
  - `/www/wwwlogs/tcp-access.log`
- **站点日志**：
  - `/www/wwwlogs/shuimitao.online.log`
  - `/www/wwwlogs/shuimitao.online.error.log`
  - `/www/wwwlogs/www.shuimitao.online.log`
  - `/www/wwwlogs/www.shuimitao.online.error.log`
  - `/www/wwwlogs/dify.shuimitao.online.log`
  - `/www/wwwlogs/dify.shuimitao.online.error.log`
  - `/www/wwwlogs/n8n.shuimitao.online.log` (Express 应用前端与API)
  - `/www/wwwlogs/n8n.shuimitao.online.error.log` (Express 应用前端与API)

### 5.3 PHP配置
- **版本**：8.2.27
- **配置文件**：`/www/server/php/82/etc/php-cli.ini`

## 六、FTP服务配置

### 6.1 Pure-FTPD信息
- **连接信息**：
  - `***********************************************` (主账户)
  - `******************************************` (FTP用户，目录 `/www/wwwroot/express`)
- **状态**：活跃运行中（通过bt服务管理）
- **关键配置**：
  - 宝塔面板管理的Pure-FTPd配置文件路径: `/www/server/pure-ftpd/etc/pure-ftpd.conf`
  - 宝塔面板管理的Pure-FTPd用户数据库: `/www/server/pure-ftpd/etc/pureftpd.pdb`

## 七、系统监控与安全

### 7.1 日志监控
- **MySQL错误日志**：`/www/server/data/VM-4-11-centos.err`
- **Nginx警告**：冲突的服务器名称 "www.shuimitao.online" 在 0.0.0.0:443

### 7.2 系统管理
- **管理员权限**：`root` 用户直接管理
- **未部署外部监控工具**：如 Zabbix/Prometheus

## 八、计划任务 (Crontab)

*(本节内容待补充，可以记录例如：数据库备份、日志清理、证书续期检查等)*

## 九、Express 应用 (Express.js 后端) 环境配置

### 9.1 基本信息
- **项目名称 (宝塔面板)**：`express`
- **项目路径**：`/www/wwwroot/express`
- **后端应用端口**：3000 (由 Express.js 应用监听，并由宝塔面板配置)
- **后端启动命令/方式 (宝塔面板配置)**：启动选项为 `npm run startbackend` (此处的 `startbackend` 是 `/www/wwwroot/express/backend/package.json` 中 `scripts` 定义的命令)。宝塔面板会自动获取 `package.json` 中的启动模式。
- **运行用户**：www
- **应用类型**：Express.js API 服务 (后端)。
- **部署时间**：(根据实际最新部署日期更新)

### 9.2 应用配置
- **项目包名 (后端)**：`express-pipeline-system` (取自 `backend/package.json`)
- **版本 (后端)**：`1.0.0` (取自 `backend/package.json`)
- **后端主入口文件**：`backend/src/app.js` (位于项目根目录 `/www/wwwroot/express/` 下的 `backend` 子目录中)
- **前端构建目录 (Nginx 静态资源)**: `frontend/dist/` (位于 `/www/wwwroot/express/frontend/dist/`)
- **核心依赖包 (后端)**：
  - express (Web框架)
  - cors (跨域资源共享)
  - dotenv (环境变量加载)
  - winston (日志记录)
  - multer (文件上传处理)
  - fluent-ffmpeg (视频/音频处理)
  - microsoft-cognitiveservices-speech-sdk (Azure AI Speech 服务)
  - axios (HTTP客户端)
- **环境变量配置文件 (后端)**: `backend/.env` (实际路径: `/www/wwwroot/express/backend/.env`)
  - **说明**: 此文件包含敏感配置和运行所需参数，**不应提交到Git仓库**。
  - **关键变量示例 (需在服务器上手动配置)**:
    - `FFMPEG_PATH=/usr/bin/ffmpeg`
    - `FFPROBE_PATH=/usr/bin/ffprobe`
    - `AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint`
    - `AZURE_OPENAI_KEY=your_azure_openai_key`
    - `WHISPER_DEPLOYMENT_NAME=your_whisper_deployment_name`
    - `DIFY_API_ENDPOINT=your_dify_api_endpoint`
    - `DIFY_API_KEY=your_dify_api_key`
    - `UPLOAD_DIR=./uploads/` (相对于 `backend` 目录, 解析为 `/www/wwwroot/express/backend/uploads/`)
    - `LOG_DIR=./logs/` (相对于 `backend` 目录, 解析为 `/www/wwwroot/express/backend/logs/`)
    - `LOG_LEVEL=info` (可选, 控制Winston日志级别)
    - `NODE_ENV=production` (或 `development`)

### 9.3 后端进程管理 (PM2) 与日志
- **PM2 进程名称 (宝塔面板管理下的名称)**: `express` (根据宝塔面板显示，例如 PID `11955, 11973`)
- **主要管理方式**: **通过宝塔Linux面板的Node项目管理器进行应用的启动、停止、重启和配置管理。**
- **Node.js 版本 (运行环境)**: `v16.20.2` (通常为 `www` 用户环境的 Node 版本)
- **包管理器**: npm (用于依赖安装)
- **PM2 日志 (底层)**:
    - 通过命令查看 (辅助排查): `sudo -u www pm2 logs express`
    - 物理路径: 通常在 `~www/.pm2/logs/` (例如 `~www/.pm2/logs/express-out.log` 和 `~www/.pm2/logs/express-error.log`)
- **应用内部详细日志 (Winston)**:
    - 路径: `/www/wwwroot/express/backend/logs/` (`app.log`, `error.log`)

### 9.4 CORS配置 (后端 Express 应用)
- **配置位置**: `backend/src/app.js` 或相关 CORS 中间件。
- **建议允许源 (示例)**:
  - `https://n8n.shuimitao.online` (生产环境前端)
  - `http://localhost:8080` (本地 Vue 前端开发服务器)
  - (根据需要添加其他源)
- **允许方法**: GET, POST, OPTIONS (通常需要 OPTIONS 进行预检请求)
- **允许凭证**: 根据需求配置 (`credentials: true`)

### 9.5 数据库连接配置 (如 Express 应用需直连)
- **当前 Express 应用**: 未直接配置连接 WordPress 数据库 `shuimitao_online`。主要通过调用外部 API (如 Dify API) 进行数据交互。

### 9.6 API端点定义
- **Nginx 代理路径**: 所有后端 API 请求通过 Nginx 的 `/api/` 路径访问。
- **后端 Express 路由**: Express 应用中定义的路由例如 `/test`, `/upload` 等，通过 Nginx 访问时对应的完整路径为 `/api/test`, `/api/upload`。

### 9.7 安全配置
- **后端监听地址**: `127.0.0.1` (由 PM2 启动的 Node.js 应用监听，不直接暴露公网)。
- **文件系统权限** (针对 `/www/wwwroot/express` 目录及其内容):
  - 目录: `755` (drwxr-xr-x)
  - 文件: `644` (-rw-r--r--)
- **文件所有者/组**: `www:www` (确保 Express 应用有权读写其工作目录及 `backend/uploads/`, `backend/logs/`, `frontend/dist/` 等)。

### 9.8 应用维护指南
- **应用启停/重启**: **主要通过宝塔Linux面板 -> 网站 -> Node项目 -> 找到对应项目 (通常名为 `express` 或基于 `backend/src/app.js` 路径) 进行操作 (启动、停止、重启、查看日志等)。**
- **查看后端日志**:
    - **宝塔面板**: 通过Node项目管理界面通常可以直接查看实时日志和历史日志。
    - PM2 (底层, 辅助排查): `sudo -u www pm2 logs express`
    - Winston (应用内部日志): 查看 `/www/wwwroot/express/backend/logs/app.log` 和 `error.log`。
- **代码更新与部署流程**:
  1.  **本地开发**: 完成代码修改 (前端和/或后端)。
  2.  **提交到 Git**: `git add .`, `git commit -m "描述信息"`, `git push origin main`。
  3.  **服务器操作 (SSH登录)**:
      a.  进入项目根目录: `cd /www/wwwroot/express`
      b.  拉取最新代码: `sudo -u www git pull origin main` (配置好无密码拉取或使用PAT)
      c.  **后端更新**:
          i.  进入后端目录: `cd backend`
          ii. 安装/更新依赖: `sudo -u www npm install` (如果 `package.json` 变动)
          iii.返回项目根目录: `cd ..`
      d.  **通过宝塔面板重启 Node.js 项目 `express`** (或宝塔中对应的项目名称)。
- **常见问题排查**:
  1.  **前端访问404/错误**:
      *   检查 Nginx 配置 (`/www/server/panel/vhost/nginx/node_express.conf`) 中 `location /` 的 `root` 指向是否为 `/www/wwwroot/express/frontend/dist`。
      *   确保前端已成功构建 (`frontend/dist` 目录存在且包含 `index.html` 等文件)。
      *   浏览器开发者工具：检查控制台错误和网络请求。
  2.  **API请求失败 (`/api/...`)**:
      *   Nginx 配置：`location /api/` 的 `proxy_pass` 是否为 `http://127.0.0.1:3000`。
      *   后端状态：`sudo -u www pm2 list` 确认 `express` 在线；`netstat -tulnp | grep 3000` 确认监听。
      *   后端日志：PM2 和 Winston 日志。
      *   CORS：确认后端允许前端源。
  3.  **文件上传/处理失败**:
      *   目录权限：`backend/uploads/` 对 `www` 用户可写。
      *   后端日志：检查 `multer`, `fluent-ffmpeg` 相关错误。
  4.  **权限问题 (通用)**:
      *   确保 `/www/wwwroot/express` 及子目录（`backend/uploads`, `backend/logs`, `frontend/dist`）所有权为 `www:www`，权限设置合理。

### 9.9 应用备份策略
- **代码备份**: 主要通过 Git 仓库 (`https://github.com/sunshine6666666666/express.git`)。
- **用户上传文件备份**: 定期备份 `/www/wwwroot/express/backend/uploads/` 目录。
  ```bash
  # 示例备份命令
  sudo tar -zcvf /www/backup/express_uploads_$(date +%Y%m%d).tar.gz /www/wwwroot/express/backend/uploads
  ```
- **日志文件**: 根据需要备份或配置日志轮换 (PM2 和 Winston 均有相关配置选项)。
- **建议备份周期**: 根据数据重要性和变更频率确定。

### 9.10 Nginx 反向代理配置 (域名: `n8n.shuimitao.online`)
- **目标**:
    - 根路径 `/`: 反向代理至 Express.js 后端，由后端返回 "Hello World" HTML。
    - 路径 `/api/`: 反向代理至 Express.js 后端 API。
- **主配置文件路径**: `/www/server/panel/vhost/nginx/node_express.conf`
    - **说明**: 此为宝塔面板为域名 `n8n.shuimitao.online` 管理的 Nginx 配置文件，已手动修改以适应当前部署架构（前端移除，根路径由后端处理）。
- **关键配置结构 (已确认)**:
    ```nginx
    server {
        listen 80;
        listen 443 ssl http2; # 已修改为同时启用SSL和HTTP/2，解决"duplicate http2 directive"错误
        listen 443 quic; # 如果您确认 QUIC/HTTP3 已配置并需要
        server_name n8n.shuimitao.online;
        client_max_body_size 1024m;

        access_log /www/wwwlogs/n8n.shuimitao.online.log;
        error_log /www/wwwlogs/n8n.shuimitao.online.error.log;

        location / { # 根路径也代理到后端
            proxy_pass http://127.0.0.1:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        location /api/ {
            proxy_pass http://127.0.0.1:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        location ~ /\\.ht { # 禁止访问 .htaccess
            deny all;
        }
    }
    ```
- **注意事项**:
    1.  直接编辑此文件后，宝塔面板的"网站设置"界面可能无法完全反映手动更改。
    2.  确保没有其他活动的、冲突的 Nginx server 块配置 `n8n.shuimitao.online`。

### 9.11 应用与 Nginx 日志路径总结
- **后端应用日志 (PM2)**:
    - 命令: `sudo -u www pm2 logs express`
    - 文件: `~www/.pm2/logs/express-out.log` (标准输出), `~www/.pm2/logs/express-error.log` (标准错误)
- **后端应用日志 (Winston)**:
    - 目录: `/www/wwwroot/express/backend/logs/` (`app.log`, `error.log`)
- **Nginx 日志 (针对 `n8n.shuimitao.online`)**:
    - 访问: `/www/wwwlogs/n8n.shuimitao.online.log`
    - 错误: `/www/wwwlogs/n8n.shuimitao.online.error.log`

## 十、关键运维问题及解决方案摘要

### 10.1 FTP登录认证失败 (530 Error) 排查与解决
- **初期症状**: FTP用户 (如 `express`, `test`) 即使密码正确也无法登录，日志显示 `530 Login authentication failed`。Pure-FTPd日志显示尝试进行PAM/Unix认证 (`pam_unix(pure-ftpd:auth): check pass; user unknown`)。
- **原因分析**:
    1.  系统运行的 `pure-ftpd.service` 加载了错误的配置文件 (`/etc/pure-ftpd/pure-ftpd.conf`)，而非宝塔面板管理的配置文件 (`/www/server/pure-ftpd/etc/pure-ftpd.conf`)。
    2.  在修正上述问题后，进一步发现宝塔管理的PureDB文件 (`/www/server/pure-ftpd/etc/pureftpd.pdb`) 权限过于严格 (`-rw-------` 或 `600`)，导致Pure-FTPd服务进程无法读取。
- **解决方案**:
    1.  **修正systemd服务单元文件**:
        -   备份: `sudo cp /usr/lib/systemd/system/pure-ftpd.service /usr/lib/systemd/system/pure-ftpd.service.bak`
        -   编辑: `sudo nano /usr/lib/systemd/system/pure-ftpd.service`
        -   修改 `ExecStart=` 行，将其中的配置文件路径从 `/etc/pure-ftpd/pure-ftpd.conf` 改为 `/www/server/pure-ftpd/etc/pure-ftpd.conf`。
        -   重载systemd: `sudo systemctl daemon-reload`
        -   重启服务: `sudo systemctl restart pure-ftpd.service`
    2.  **修正PureDB文件权限**:
        -   命令: `sudo chmod 644 /www/server/pure-ftpd/etc/pureftpd.pdb`
    3.  **最终生效**: 在上述所有配置和权限修正后，通过**宝塔面板界面重启Pure-FTPd服务**，确保所有设置被正确应用，FTP用户恢复正常登录。
- **关键配置文件确认**: `/www/server/pure-ftpd/etc/pure-ftpd.conf` 中应确保以下配置正确，以使用宝塔管理的虚拟用户:
    ```ini
    UnixAuthentication     no
    PAMAuthentication      no
    PureDB                 /www/server/pure-ftpd/etc/pureftpd.pdb
    ```

### 10.2 Nginx 配置警告排查与解决
- **问题现象**:
    1.  `nginx: [warn] the "listen ... http2" directive is deprecated, use the "http2" directive instead in /www/server/panel/vhost/nginx/node_express.conf:3`
    2.  `nginx: [warn] conflicting server name "www.shuimitao.online" on 0.0.0.0:443, ignored`
- **根本原因**:
    1.  **HTTP/2 警告**: `node_express.conf` 文件中，最初存在 `listen 443 ssl;` 和 `http2 on;` 两条指令，导致"duplicate http2 directive"错误。在修复后，Nginx 1.25.5 版本提示 `listen ... http2` 语法已弃用，推荐使用独立的 `http2 on;` 指令。此警告不影响 HTTP/2 功能的实际启用。
    2.  **冲突的服务器名称警告**: 在 Nginx 加载的所有配置中，存在至少两个 `server` 块都试图在 443 端口上处理 `www.shuimitao.online` 这个域名。其中一个位于 `/www/server/panel/vhost/nginx/www.shuimitao.online.conf`，另一个冲突源未明确识别，但 Nginx 已选择一个处理，并忽略了另一个。
- **影响范围**:
    1.  **HTTP/2 警告**: 仅为语法建议，不影响功能，也不会导致服务中断。
    2.  **冲突的服务器名称警告**: 可能导致 `www.shuimitao.online` 网站功能异常或日志记录不准确，但当前网站运行正常，此问题被暂时搁置。
- **解决方案**:
    1.  **解决"duplicate http2 directive"错误**:
        -   **修改理由**: Nginx 报错 `duplicate http2 directive`，导致配置无法加载。
        -   **修改方法**: 在 `/www/server/panel/vhost/nginx/node_express.conf` 文件中，将 `listen 443 ssl;` 修改为 `listen 443 ssl http2;`，并移除单独的 `http2 on;` 指令。
        -   **预期效果**: Nginx 配置能够成功加载，且 `n8n.shuimitao.online` 站点在 HTTPS 上启用 HTTP/2。
    2.  **处理"listen ... http2"弃用警告**:
        -   **修改理由**: 消除 Nginx 警告，遵循推荐的最佳实践。
        -   **修改方法**: 目前暂时搁置，不影响功能。未来可考虑将 `listen 443 ssl http2;` 更改为 `listen 443 ssl;` 并在 `server` 块内添加 `http2 on;`。
    3.  **处理"conflicting server name"警告**:
        -   **修改理由**: 消除 Nginx 警告，确保 `www.shuimitao.online` 网站的配置清晰且唯一。
        -   **修改方法**: 由于当前网站运行正常且无时间，此问题暂时搁置。未来需要找出并禁用或移除冲突的第二个 `server` 块。
        -   **预期效果**: Nginx 警告消除，`www.shuimitao.online` 网站的配置行为更可预测。
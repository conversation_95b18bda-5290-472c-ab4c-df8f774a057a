<?php
/*

Template Name Posts: myBook

*/

if ($_GET['action']) {
    session_start();
    session_unset();
}

$userData = new userDataTest();


$reviewMesHandle = new reviewMesHandle($userData->user_id, $userData->isHeikaVip, current_time("timestamp"));//初始化记忆系统对象

pcVisitHandle();
get_header();//头部

if ($userData->isLogin) {
    if (!$userData->isVip) {
        require_once "includes/element/goVip.php";
        echo '<script type="text/javascript">';
        echo 'var goVip=$(".goVip");';
        echo 'goVip.css("display","block");';
        echo '</script>';

    } else {
    //未登录状态无法访问

    global $post;

        if(!is_super_admin()){
            if ($post->post_author != $userData->user_id) {//单词本拥有者与访问者不符
                echo "禁止访问";
                exit();
            }
        }

  

    global $post;

    $postData = new postData($post);
    $postData->getWordsArr();

    get_top_nav();//顶部导航

    $backUrl = home_url();
    /*单词本状态
     *1单词本在今日复习内
     * 2单词本不在今日复习内
     * 3单词本复习以完成
     * 0没有这个单词本
     * */
    $bookStatus = $reviewMesHandle->get_now_post_course_status($postData->postId);


    echo '<style type="text/css">.sub_nav {width: 100%;z-index: 997;position: fixed;top: 0px;height: 50px;background-color: #e8e8e8;}</style>';
    echo '<link rel="stylesheet"  href="';
    bloginfo('template_url');
    echo '/myBook.css?z1.1" />';
    echo '<div class="sub_nav">';
    echo '<ul class="sub_nav_menu">';
    echo '<li>';
    if ($bookStatus == 0) {
        echo '<div id="searchBtn" >搜索生词</div>';
    } else {
        echo '<div id="searchBtn" style="display: none">搜索生词</div>';
    }
    echo '<div id="wordCardBtn" class="now_menu">生词卡</div>';
    echo '<div id="reciteBtn">强化记词</div>';
    echo '<div id="myTestBtn">单词抽查</div>';
    echo '</li>';
    echo '</ul>';
    echo '</div>';
    echo '<article>';


    echo '<div class="word_card_page">';
    require_once 'includes/single/myBook/wordcard.php';
    echo '</div>';

    echo '<div class="recite_page">';
    require_once 'includes/single/myBook/recite.php';
    echo '</div>';

    echo '<div class="my_test_page">';
    require_once 'includes/single/myBook/myTest.php';
    echo '</div>';


    echo '<div class="search_word_page">';
    require_once 'includes/single/myBook/searchWord.php';
    echo '</div>';


    echo '<div class="no_word">';
    echo '<div class="weui-loadmore weui-loadmore_line" >';
    echo '<span class="no_word_text weui-loadmore__tips">暂无单词</span>';
    echo '</div>';
    echo '<button class="no_word_btn weui-loadmore__tips">去搜索</button>';
    echo '</div>';


    echo '</article>';

    $selectUrl = home_url() . "/?page_id=13794";


    register_enqueue_script('vars', '/js/myBook/vars.js?v3.0');
    $vars_array = array(
        "selectUrl"=>$selectUrl,
        "backUrl" => $backUrl,
        "bookStatus" => $bookStatus,
        "user_default_book" => $userData->user_default_book,
        "isVip" => $userData->isVip,
        "isLogin" => $userData->isLogin,
        "user_id" => $userData->user_id,
        "postId" => $postData->postId,
        "webUrl" => home_url(),
        'ajaxUrl' => $postData->ajaxUrl,
        'wordArr' => $postData->wordArr,
        'wordOriArr' => $postData->wordOriArr,
        'wordPhoneticArr' => $postData->wordPhoneticArr,
        'wordSenEnArr' => $postData->wordSenEnArr,
        'wordSenCnArr' => $postData->wordSenCnArr,
        'wordSenEnArrHasDot' => $postData->wordSenEnArrHasDot,
        'wordSenEnArrNoDot' => $postData->wordSenEnArrNoDot,
        'wordCnTrans' => $postData->wordCnTrans,
        'wordEeTrans' => $postData->wordEeTrans,
        'wordIsModifyArr' => $postData->wordIsModifyArr
    );

    wp_enqueue_script('vars');
    wp_localize_script('vars', 'object', $vars_array);

    register_enqueue_script('searchword', '/js/myBook/searchword.js?v2.9');
    wp_enqueue_script('searchword');

    register_enqueue_script('myTest', '/js/myBook/myTest.js?v2.9');
    wp_enqueue_script('myTest');


    register_enqueue_script('recite', '/js/myBook/recite.js?v2.9');
    wp_enqueue_script('recite');

    register_enqueue_script('wordbook', '/js/myBook/wordcard.js?v2.9');
    wp_enqueue_script('wordbook');

    register_enqueue_script('app', '/js/myBook/app.js?v2.9');
    wp_enqueue_script('app');





}


} else {


    ?>

    <div class="weui-msg" style="margin-top: 50px">
        <div class="weui-msg__text-area">
            <p class="weui-msg__desc" style="font-size: 16px;color: black">抱歉，您还未登录，请先登录!</p>
        </div>
        <div class="weui-msg__opr-area" style="margin-top: 120px">
            <p class="weui-btn-area">
                <a href="javascript:;" class="weui-btn weui-btn_primary attentBtn" style="width: 60%">微信登录</a>
            </p>
        </div>
    </div>

    <script type="text/javascript">
        var attentionBtn = $(".attentionBtn");
        for (var i = 0; i < attentionBtn.length; i++) {
            attentionBtn[i].onclick = function () {
                $$showWechatLogin(<?php echo '"'.get_post_meta("3376","wechat_qr_code",true).'"';?>);
            };
        }
    </script>


<?php } ?>

<?php

get_footer();//底部
?>
